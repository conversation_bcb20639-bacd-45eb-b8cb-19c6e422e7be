# Chinese Pharmacopoeia PDF Splitter Configuration File

# Application settings
app:
  name: "Chinese Pharmacopoeia PDF Splitter"
  version: "1.0.0"
  author: "PDF Processing Tool"

# PDF processing settings
pdf:
  # Supported pharmacopoeia volumes
  volumes:
    - "Volume 1"
    - "Volume 2" 
    - "Volume 3"
    - "Volume 4"
  
  # Chapter detection settings
  chapter_detection:
    # Font size threshold for chapter titles (relative to average)
    font_size_threshold: 1.2
    # Minimum confidence score for chapter detection
    confidence_threshold: 0.8
    # Keywords that indicate chapter beginnings
    chapter_keywords:
      - "第"
      - "章"
      - "部分"
      - "篇"
    
  # Output file naming settings
  output:
    # Default naming options (can be overridden by user)
    use_chinese_names: true
    include_page_numbers: true
    include_timestamp: true
    timestamp_format: "%Y%m%d%H%M"

# GUI settings
gui:
  # Window settings
  window:
    width: 800
    height: 600
    title: "Chinese Pharmacopoeia PDF Splitter"
  
  # Progress settings
  progress:
    update_interval: 100  # milliseconds
    show_current_chapter: true
    show_page_progress: true

# Logging settings
logging:
  # Log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
  level: "INFO"
  # Log file settings
  file:
    enabled: true
    directory: "logs"
    filename_format: "log_{timestamp}.log"
    max_size_mb: 10
    backup_count: 5
  # Console logging
  console:
    enabled: true
    colored: true

# Directory settings
directories:
  output: "output"
  temp: "temp"
  logs: "logs"
  tests: "tests/test_data"

# Error handling settings
error_handling:
  # Maximum retry attempts for PDF operations
  max_retries: 3
  # Timeout for PDF operations (seconds)
  operation_timeout: 30
  # Continue processing on non-critical errors
  continue_on_error: true
