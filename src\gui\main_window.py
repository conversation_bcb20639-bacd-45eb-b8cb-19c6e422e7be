"""
Main Window Module
Main GUI window for Chinese Pharmacopoeia PDF Splitter application
"""

import sys
import os
from pathlib import Path
from typing import Optional, Dict, Any
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QPushButton, QLabel, QLineEdit, QComboBox, QCheckBox, QTextEdit,
    QProgressBar, QFileDialog, QMessageBox, QGroupBox, QFrame
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon

try:
    from ..models.chapter_info import VolumeType, PDFAnalysisResult
    from ..core.pdf_analyzer import PDFAnalyzer
    from ..core.pdf_splitter import PDFSplitter, SplittingProgress
    from ..utils.config_manager import get_config_manager
    from ..utils.logger import get_logger
    from ..utils.file_utils import get_file_manager
except ImportError:
    # Fallback for direct execution
    from models.chapter_info import VolumeType, PDFAnalysisResult
    from core.pdf_analyzer import PDFAnalyzer
    from core.pdf_splitter import PDFSplitter, SplittingProgress
    from utils.config_manager import get_config_manager
    from utils.logger import get_logger
    from utils.file_utils import get_file_manager


class AnalysisWorker(QThread):
    """Worker thread for PDF analysis"""
    
    progress_updated = pyqtSignal(str)  # Progress message
    analysis_completed = pyqtSignal(object)  # PDFAnalysisResult
    analysis_failed = pyqtSignal(str)  # Error message
    
    def __init__(self, pdf_path: str, volume: VolumeType):
        super().__init__()
        self.pdf_path = pdf_path
        self.volume = volume
        self.analyzer = PDFAnalyzer()
    
    def run(self):
        """Run PDF analysis in background thread"""
        try:
            self.progress_updated.emit("Starting PDF analysis...")
            result = self.analyzer.analyze_pdf(self.pdf_path, self.volume)
            self.analysis_completed.emit(result)
        except Exception as e:
            self.analysis_failed.emit(str(e))
        finally:
            # Ensure thread cleanup
            self.quit()


class SplittingWorker(QThread):
    """Worker thread for PDF splitting"""
    
    progress_updated = pyqtSignal(object)  # SplittingProgress
    splitting_completed = pyqtSignal(object)  # Result summary
    splitting_failed = pyqtSignal(str)  # Error message
    
    def __init__(self, analysis_result: PDFAnalysisResult, output_dir: str,
                 use_chinese: bool, include_pages: bool, include_timestamp: bool):
        super().__init__()
        self.analysis_result = analysis_result
        self.output_dir = output_dir
        self.use_chinese = use_chinese
        self.include_pages = include_pages
        self.include_timestamp = include_timestamp
        self.splitter = PDFSplitter()
    
    def run(self):
        """Run PDF splitting in background thread"""
        try:
            result = self.splitter.split_pdf(
                self.analysis_result,
                self.output_dir,
                self.use_chinese,
                self.include_pages,
                self.include_timestamp,
                progress_callback=self.progress_updated.emit
            )
            self.splitting_completed.emit(result)
        except Exception as e:
            self.splitting_failed.emit(str(e))
        finally:
            # Ensure thread cleanup
            self.quit()
    
    def pause_splitting(self):
        """Pause splitting operation"""
        self.splitter.pause_splitting()
    
    def resume_splitting(self):
        """Resume splitting operation"""
        self.splitter.resume_splitting()
    
    def cancel_splitting(self):
        """Cancel splitting operation"""
        self.splitter.cancel_splitting()


class MainWindow(QMainWindow):
    """Main application window"""
    
    def __init__(self):
        super().__init__()
        
        # Initialize components
        self.config_manager = get_config_manager()
        self.gui_config = self.config_manager.get_gui_config()
        self.logger = get_logger()
        self.file_manager = get_file_manager()
        
        # State variables
        self.current_analysis_result: Optional[PDFAnalysisResult] = None
        self.analysis_worker: Optional[AnalysisWorker] = None
        self.splitting_worker: Optional[SplittingWorker] = None
        
        # Initialize UI
        self.init_ui()
        
        # Setup window
        self.setWindowTitle(self.gui_config.window_title)
        self.setGeometry(100, 100, self.gui_config.window_width, self.gui_config.window_height)
        
        self.logger.info("Main window initialized")
    
    def init_ui(self):
        """Initialize user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Title
        title_label = QLabel("Chinese Pharmacopoeia PDF Splitter")
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)
        
        # Input section
        input_group = self.create_input_section()
        main_layout.addWidget(input_group)
        
        # Options section
        options_group = self.create_options_section()
        main_layout.addWidget(options_group)
        
        # Control buttons
        control_layout = self.create_control_section()
        main_layout.addLayout(control_layout)
        
        # Progress section
        progress_group = self.create_progress_section()
        main_layout.addWidget(progress_group)
        
        # Log section
        log_group = self.create_log_section()
        main_layout.addWidget(log_group)
        
        # Status bar
        self.statusBar().showMessage("Ready")
    
    def create_input_section(self) -> QGroupBox:
        """Create input file selection section"""
        group = QGroupBox("Input Settings")
        layout = QGridLayout(group)
        
        # PDF file selection
        layout.addWidget(QLabel("PDF File:"), 0, 0)
        self.pdf_path_edit = QLineEdit()
        self.pdf_path_edit.setPlaceholderText("Select Chinese Pharmacopoeia PDF file...")
        layout.addWidget(self.pdf_path_edit, 0, 1)
        
        self.browse_button = QPushButton("Browse...")
        self.browse_button.clicked.connect(self.browse_pdf_file)
        layout.addWidget(self.browse_button, 0, 2)
        
        # Volume selection
        layout.addWidget(QLabel("Volume:"), 1, 0)
        self.volume_combo = QComboBox()
        self.volume_combo.addItems([vol.value for vol in VolumeType])
        layout.addWidget(self.volume_combo, 1, 1, 1, 2)
        
        # Output directory
        layout.addWidget(QLabel("Output Directory:"), 2, 0)
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setText("output")
        layout.addWidget(self.output_dir_edit, 2, 1)
        
        self.browse_output_button = QPushButton("Browse...")
        self.browse_output_button.clicked.connect(self.browse_output_directory)
        layout.addWidget(self.browse_output_button, 2, 2)
        
        return group
    
    def create_options_section(self) -> QGroupBox:
        """Create options section"""
        group = QGroupBox("Output Options")
        layout = QVBoxLayout(group)
        
        # Filename options
        self.use_chinese_checkbox = QCheckBox("Use Chinese characters in filenames")
        self.use_chinese_checkbox.setChecked(True)
        layout.addWidget(self.use_chinese_checkbox)
        
        self.include_pages_checkbox = QCheckBox("Include page numbers in filenames")
        self.include_pages_checkbox.setChecked(True)
        layout.addWidget(self.include_pages_checkbox)
        
        self.include_timestamp_checkbox = QCheckBox("Include timestamp in filenames")
        self.include_timestamp_checkbox.setChecked(True)
        layout.addWidget(self.include_timestamp_checkbox)
        
        return group
    
    def create_control_section(self) -> QHBoxLayout:
        """Create control buttons section"""
        layout = QHBoxLayout()
        
        # Analyze button
        self.analyze_button = QPushButton("Analyze PDF")
        self.analyze_button.clicked.connect(self.analyze_pdf)
        layout.addWidget(self.analyze_button)
        
        # Split button
        self.split_button = QPushButton("Split PDF")
        self.split_button.clicked.connect(self.split_pdf)
        self.split_button.setEnabled(False)
        layout.addWidget(self.split_button)
        
        # Control buttons
        self.pause_button = QPushButton("Pause")
        self.pause_button.clicked.connect(self.pause_operation)
        self.pause_button.setEnabled(False)
        layout.addWidget(self.pause_button)
        
        self.resume_button = QPushButton("Resume")
        self.resume_button.clicked.connect(self.resume_operation)
        self.resume_button.setEnabled(False)
        layout.addWidget(self.resume_button)
        
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.clicked.connect(self.cancel_operation)
        self.cancel_button.setEnabled(False)
        layout.addWidget(self.cancel_button)
        
        layout.addStretch()
        
        return layout
    
    def create_progress_section(self) -> QGroupBox:
        """Create progress tracking section"""
        group = QGroupBox("Progress")
        layout = QVBoxLayout(group)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # Progress labels
        self.progress_label = QLabel("Ready")
        layout.addWidget(self.progress_label)
        
        self.current_chapter_label = QLabel("")
        layout.addWidget(self.current_chapter_label)
        
        return group
    
    def create_log_section(self) -> QGroupBox:
        """Create log display section"""
        group = QGroupBox("Log")
        layout = QVBoxLayout(group)
        
        self.log_text = QTextEdit()
        self.log_text.setMaximumHeight(150)
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        return group
    
    def browse_pdf_file(self):
        """Browse for PDF file"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "Select Chinese Pharmacopoeia PDF",
            "",
            "PDF Files (*.pdf);;All Files (*)"
        )
        
        if file_path:
            self.pdf_path_edit.setText(file_path)
            self.log_message(f"Selected PDF file: {file_path}")
    
    def browse_output_directory(self):
        """Browse for output directory"""
        directory = QFileDialog.getExistingDirectory(
            self,
            "Select Output Directory"
        )
        
        if directory:
            self.output_dir_edit.setText(directory)
            self.log_message(f"Selected output directory: {directory}")
    
    def analyze_pdf(self):
        """Start PDF analysis"""
        pdf_path = self.pdf_path_edit.text().strip()
        
        if not pdf_path:
            QMessageBox.warning(self, "Warning", "Please select a PDF file.")
            return
        
        if not Path(pdf_path).exists():
            QMessageBox.warning(self, "Warning", "Selected PDF file does not exist.")
            return
        
        # Get selected volume
        volume_text = self.volume_combo.currentText()
        volume = VolumeType(volume_text)
        
        # Disable controls
        self.set_controls_enabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # Indeterminate progress
        
        # Start analysis worker
        self.analysis_worker = AnalysisWorker(pdf_path, volume)
        self.analysis_worker.progress_updated.connect(self.update_analysis_progress)
        self.analysis_worker.analysis_completed.connect(self.analysis_completed)
        self.analysis_worker.analysis_failed.connect(self.analysis_failed)
        self.analysis_worker.start()
        
        self.log_message("Starting PDF analysis...")
    
    def split_pdf(self):
        """Start PDF splitting"""
        if not self.current_analysis_result:
            QMessageBox.warning(self, "Warning", "Please analyze PDF first.")
            return
        
        output_dir = self.output_dir_edit.text().strip()
        if not output_dir:
            QMessageBox.warning(self, "Warning", "Please specify output directory.")
            return
        
        # Get options
        use_chinese = self.use_chinese_checkbox.isChecked()
        include_pages = self.include_pages_checkbox.isChecked()
        include_timestamp = self.include_timestamp_checkbox.isChecked()
        
        # Disable controls and enable operation controls
        self.set_controls_enabled(False)
        self.set_operation_controls_enabled(True)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, len(self.current_analysis_result.chapters))
        self.progress_bar.setValue(0)
        
        # Start splitting worker
        self.splitting_worker = SplittingWorker(
            self.current_analysis_result,
            output_dir,
            use_chinese,
            include_pages,
            include_timestamp
        )
        self.splitting_worker.progress_updated.connect(self.update_splitting_progress)
        self.splitting_worker.splitting_completed.connect(self.splitting_completed)
        self.splitting_worker.splitting_failed.connect(self.splitting_failed)
        self.splitting_worker.start()
        
        self.log_message("Starting PDF splitting...")
    
    def pause_operation(self):
        """Pause current operation"""
        if self.splitting_worker:
            self.splitting_worker.pause_splitting()
            self.pause_button.setEnabled(False)
            self.resume_button.setEnabled(True)
            self.log_message("Operation paused")
    
    def resume_operation(self):
        """Resume current operation"""
        if self.splitting_worker:
            self.splitting_worker.resume_splitting()
            self.pause_button.setEnabled(True)
            self.resume_button.setEnabled(False)
            self.log_message("Operation resumed")
    
    def cancel_operation(self):
        """Cancel current operation"""
        if self.splitting_worker:
            self.splitting_worker.cancel_splitting()
            self.log_message("Operation cancelled")
    
    def update_analysis_progress(self, message: str):
        """Update analysis progress"""
        self.progress_label.setText(message)
        self.log_message(message)
    
    def analysis_completed(self, result: PDFAnalysisResult):
        """Handle analysis completion"""
        self.current_analysis_result = result

        # Clean up worker thread properly
        if self.analysis_worker:
            self.analysis_worker.quit()
            self.analysis_worker.wait(1000)  # Wait up to 1 second
            self.analysis_worker.deleteLater()
            self.analysis_worker = None

        # Update UI
        self.progress_bar.setVisible(False)
        self.set_controls_enabled(True)
        self.split_button.setEnabled(True)

        # Show results
        chapter_count = len(result.chapters)
        message = f"Analysis completed. Found {chapter_count} chapters."
        self.progress_label.setText(message)
        self.log_message(message)

        # Show chapter list
        self.show_chapter_list(result.chapters)
    
    def analysis_failed(self, error_message: str):
        """Handle analysis failure"""
        # Clean up worker thread
        if self.analysis_worker:
            if self.analysis_worker.isRunning():
                self.analysis_worker.quit()
                self.analysis_worker.wait(3000)  # Wait up to 3 seconds
            self.analysis_worker = None

        self.progress_bar.setVisible(False)
        self.set_controls_enabled(True)

        error_msg = f"Analysis failed: {error_message}"
        self.progress_label.setText("Analysis failed")
        self.log_message(error_msg)

        QMessageBox.critical(self, "Analysis Error", error_msg)
    
    def update_splitting_progress(self, progress: SplittingProgress):
        """Update splitting progress"""
        self.progress_bar.setValue(progress.current_chapter)
        
        progress_text = f"Processing {progress.current_chapter}/{progress.total_chapters} chapters"
        self.progress_label.setText(progress_text)
        
        if progress.current_chapter_name:
            self.current_chapter_label.setText(f"Current: {progress.current_chapter_name}")
    
    def splitting_completed(self, result_summary: Dict[str, Any]):
        """Handle splitting completion"""
        # Clean up worker thread properly
        if self.splitting_worker:
            self.splitting_worker.quit()
            self.splitting_worker.wait(1000)  # Wait up to 1 second
            self.splitting_worker.deleteLater()
            self.splitting_worker = None

        # Update UI
        self.progress_bar.setVisible(False)
        self.set_controls_enabled(True)
        self.set_operation_controls_enabled(False)
        
        # Show results
        success_count = result_summary['successful_splits']
        failed_count = result_summary['failed_splits']
        
        message = f"Splitting completed. Success: {success_count}, Failed: {failed_count}"
        self.progress_label.setText(message)
        self.current_chapter_label.setText("")
        self.log_message(message)
        
        # Show completion dialog
        if failed_count == 0:
            QMessageBox.information(self, "Success", f"All {success_count} chapters split successfully!")
        else:
            QMessageBox.warning(self, "Partial Success", 
                              f"Splitting completed with {failed_count} failures. "
                              f"Check log for details.")
    
    def splitting_failed(self, error_message: str):
        """Handle splitting failure"""
        # Clean up worker thread properly
        if self.splitting_worker:
            self.splitting_worker.quit()
            self.splitting_worker.wait(1000)  # Wait up to 1 second
            self.splitting_worker.deleteLater()
            self.splitting_worker = None

        self.progress_bar.setVisible(False)
        self.set_controls_enabled(True)
        self.set_operation_controls_enabled(False)
        
        error_msg = f"Splitting failed: {error_message}"
        self.progress_label.setText("Splitting failed")
        self.current_chapter_label.setText("")
        self.log_message(error_msg)
        
        QMessageBox.critical(self, "Splitting Error", error_msg)
    
    def show_chapter_list(self, chapters):
        """Show detected chapters in log"""
        self.log_message("Detected chapters:")
        for i, chapter in enumerate(chapters, 1):
            self.log_message(f"  {i}. {chapter.title} (pages {chapter.page_range_str})")
    
    def set_controls_enabled(self, enabled: bool):
        """Enable/disable main controls"""
        self.browse_button.setEnabled(enabled)
        self.browse_output_button.setEnabled(enabled)
        self.volume_combo.setEnabled(enabled)
        self.analyze_button.setEnabled(enabled)
        self.use_chinese_checkbox.setEnabled(enabled)
        self.include_pages_checkbox.setEnabled(enabled)
        self.include_timestamp_checkbox.setEnabled(enabled)
    
    def set_operation_controls_enabled(self, enabled: bool):
        """Enable/disable operation controls"""
        self.pause_button.setEnabled(enabled)
        self.cancel_button.setEnabled(enabled)
        self.resume_button.setEnabled(False)  # Resume starts disabled
    
    def log_message(self, message: str):
        """Add message to log"""
        self.log_text.append(message)
        self.log_text.ensureCursorVisible()
    
    def closeEvent(self, event):
        """Handle window close event"""
        # Cancel any running operations gracefully
        if self.analysis_worker and self.analysis_worker.isRunning():
            self.analysis_worker.quit()
            if not self.analysis_worker.wait(3000):  # Wait up to 3 seconds
                self.analysis_worker.terminate()
                self.analysis_worker.wait(1000)  # Wait up to 1 second for termination

        if self.splitting_worker and self.splitting_worker.isRunning():
            self.splitting_worker.cancel_splitting()
            if not self.splitting_worker.wait(3000):  # Wait up to 3 seconds
                self.splitting_worker.terminate()
                self.splitting_worker.wait(1000)  # Wait up to 1 second for termination

        event.accept()


def create_main_window() -> MainWindow:
    """
    Create and return main window instance

    Returns:
        MainWindow: Main window instance
    """
    return MainWindow()
