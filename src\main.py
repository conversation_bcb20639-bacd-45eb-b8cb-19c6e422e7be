"""
Main Application Module
Entry point for Chinese Pharmacopoeia PDF Splitter application
"""

import sys
import os
from pathlib import Path
from PyQt6.QtWidgets import QApplication, QMessageBox
from PyQt6.QtCore import Qt

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from gui.main_window import create_main_window
from utils.config_manager import get_config_manager
from utils.logger import get_logger
from utils.file_utils import get_file_manager


def setup_application() -> bool:
    """
    Setup application environment and dependencies
    
    Returns:
        bool: True if setup successful, False otherwise
    """
    try:
        # Initialize configuration manager
        config_manager = get_config_manager()
        
        # Initialize logger
        logger = get_logger()
        logger.info("Starting Chinese Pharmacopoeia PDF Splitter")
        
        # Initialize file manager
        file_manager = get_file_manager()
        
        # Ensure required directories exist
        dirs_config = config_manager.get_directories_config()
        directories = [
            dirs_config.output,
            dirs_config.temp,
            dirs_config.logs
        ]
        
        for directory in directories:
            if not file_manager.ensure_directory(directory):
                logger.error(f"Failed to create directory: {directory}")
                return False
        
        logger.info("Application setup completed successfully")
        return True
        
    except Exception as e:
        print(f"Failed to setup application: {e}")
        return False


def create_application() -> QApplication:
    """
    Create and configure QApplication instance
    
    Returns:
        QApplication: Configured application instance
    """
    # Create application
    app = QApplication(sys.argv)
    
    # Set application properties
    config_manager = get_config_manager()
    app_config = config_manager.get_app_config()
    
    app.setApplicationName(app_config.name)
    app.setApplicationVersion(app_config.version)
    app.setOrganizationName(app_config.author)
    
    # Set application style
    app.setStyle('Fusion')  # Use Fusion style for consistent appearance
    
    return app


def show_error_dialog(title: str, message: str) -> None:
    """
    Show error dialog to user
    
    Args:
        title: Dialog title
        message: Error message
    """
    try:
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        msg_box = QMessageBox()
        msg_box.setIcon(QMessageBox.Icon.Critical)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.exec()
        
    except Exception:
        # Fallback to console output if GUI fails
        print(f"ERROR - {title}: {message}")


def run_application() -> int:
    """
    Main application entry point
    
    Returns:
        int: Application exit code
    """
    try:
        # Setup application environment
        if not setup_application():
            show_error_dialog(
                "Setup Error",
                "Failed to setup application environment. Please check configuration and try again."
            )
            return 1
        
        # Create QApplication
        app = create_application()
        
        # Create and show main window
        try:
            main_window = create_main_window()
            main_window.show()
            
            # Get logger after setup
            logger = get_logger()
            logger.info("Main window displayed, application ready")
            
        except Exception as e:
            logger = get_logger()
            logger.error(f"Failed to create main window: {e}")
            show_error_dialog(
                "Window Creation Error",
                f"Failed to create main window: {str(e)}"
            )
            return 1
        
        # Run application event loop
        try:
            exit_code = app.exec()
            logger.info(f"Application exiting with code: {exit_code}")
            return exit_code
            
        except Exception as e:
            logger = get_logger()
            logger.error(f"Application runtime error: {e}")
            show_error_dialog(
                "Runtime Error",
                f"Application encountered a runtime error: {str(e)}"
            )
            return 1
    
    except Exception as e:
        # Handle any unexpected errors
        error_msg = f"Unexpected error: {str(e)}"
        print(error_msg)  # Print to console as fallback
        
        show_error_dialog("Critical Error", error_msg)
        return 1


def main():
    """Main function for direct execution"""
    sys.exit(run_application())


if __name__ == "__main__":
    main()
