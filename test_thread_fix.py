#!/usr/bin/env python3
"""
Test script to verify thread management fixes
Tests proper thread cleanup and prevents "thread still running" errors
"""

import sys
import time
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

try:
    from PyQt6.QtWidgets import QApplication
    from PyQt6.QtCore import QThread, QTimer
    from gui.main_window import AnalysisWorker, SplittingWorker
    from models.chapter_info import VolumeType, PDFAnalysisResult
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Please ensure PyQt6 is installed: pip install PyQt6")
    sys.exit(1)


class ThreadTester:
    """Test thread management"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.analysis_worker = None
        self.splitting_worker = None
        self.test_results = []
    
    def test_analysis_worker_cleanup(self):
        """Test AnalysisWorker thread cleanup"""
        print("Testing AnalysisWorker thread cleanup...")
        
        # Find a PDF file for testing
        pdf_files = list(Path(".").glob("*.pdf"))
        if not pdf_files:
            print("⚠️  No PDF files found, skipping analysis worker test")
            return True
        
        test_pdf = pdf_files[0]
        
        try:
            # Create worker
            self.analysis_worker = AnalysisWorker(str(test_pdf), VolumeType.VOLUME_1)
            
            # Connect signals
            self.analysis_worker.analysis_completed.connect(self.on_analysis_completed)
            self.analysis_worker.analysis_failed.connect(self.on_analysis_failed)
            
            # Start worker
            self.analysis_worker.start()
            
            # Wait a bit for thread to start
            time.sleep(0.5)
            
            # Check if thread is running
            if self.analysis_worker.isRunning():
                print("✅ AnalysisWorker started successfully")
                
                # Wait for completion or timeout
                if self.analysis_worker.wait(10000):  # 10 second timeout
                    print("✅ AnalysisWorker completed within timeout")
                    
                    # Check if thread is properly finished
                    if not self.analysis_worker.isRunning():
                        print("✅ AnalysisWorker thread properly finished")
                        return True
                    else:
                        print("❌ AnalysisWorker thread still running after completion")
                        return False
                else:
                    print("⚠️  AnalysisWorker timeout (this is expected for large PDFs)")
                    # Force cleanup
                    self.analysis_worker.quit()
                    self.analysis_worker.wait(3000)
                    return True
            else:
                print("❌ AnalysisWorker failed to start")
                return False
                
        except Exception as e:
            print(f"❌ AnalysisWorker test failed: {e}")
            return False
        finally:
            # Cleanup
            if self.analysis_worker:
                if self.analysis_worker.isRunning():
                    self.analysis_worker.quit()
                    self.analysis_worker.wait(3000)
                self.analysis_worker.deleteLater()
                self.analysis_worker = None
    
    def test_splitting_worker_cleanup(self):
        """Test SplittingWorker thread cleanup"""
        print("\nTesting SplittingWorker thread cleanup...")
        
        try:
            # Create mock analysis result
            result = PDFAnalysisResult(
                pdf_path="test.pdf",
                volume=VolumeType.VOLUME_1,
                total_pages=10
            )
            
            # Create worker
            self.splitting_worker = SplittingWorker(
                result, "output", True, True, True
            )
            
            # Connect signals
            self.splitting_worker.splitting_completed.connect(self.on_splitting_completed)
            self.splitting_worker.splitting_failed.connect(self.on_splitting_failed)
            
            # Start worker (it will fail quickly due to mock data, which is what we want)
            self.splitting_worker.start()
            
            # Wait a bit
            time.sleep(0.5)
            
            # Wait for completion or timeout
            if self.splitting_worker.wait(5000):  # 5 second timeout
                print("✅ SplittingWorker completed within timeout")
                
                # Check if thread is properly finished
                if not self.splitting_worker.isRunning():
                    print("✅ SplittingWorker thread properly finished")
                    return True
                else:
                    print("❌ SplittingWorker thread still running after completion")
                    return False
            else:
                print("⚠️  SplittingWorker timeout")
                # Force cleanup
                self.splitting_worker.quit()
                self.splitting_worker.wait(3000)
                return True
                
        except Exception as e:
            print(f"❌ SplittingWorker test failed: {e}")
            return False
        finally:
            # Cleanup
            if self.splitting_worker:
                if self.splitting_worker.isRunning():
                    self.splitting_worker.quit()
                    self.splitting_worker.wait(3000)
                self.splitting_worker.deleteLater()
                self.splitting_worker = None
    
    def on_analysis_completed(self, result):
        """Handle analysis completion"""
        print("📊 Analysis completed signal received")
    
    def on_analysis_failed(self, error):
        """Handle analysis failure"""
        print(f"📊 Analysis failed signal received: {error}")
    
    def on_splitting_completed(self, result):
        """Handle splitting completion"""
        print("📊 Splitting completed signal received")
    
    def on_splitting_failed(self, error):
        """Handle splitting failure"""
        print(f"📊 Splitting failed signal received: {error}")
    
    def run_tests(self):
        """Run all thread tests"""
        print("Thread Management Fix Verification")
        print("=" * 40)
        
        test1_passed = self.test_analysis_worker_cleanup()
        test2_passed = self.test_splitting_worker_cleanup()
        
        print("\n" + "=" * 40)
        print("Test Results:")
        print(f"  AnalysisWorker cleanup: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
        print(f"  SplittingWorker cleanup: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
        
        if test1_passed and test2_passed:
            print("\n🎉 All thread management tests passed!")
            print("The application should no longer show 'thread still running' errors.")
            return 0
        else:
            print("\n❌ Some thread management tests failed.")
            return 1


def main():
    """Main test function"""
    tester = ThreadTester()
    result = tester.run_tests()
    
    # Clean exit
    tester.app.quit()
    return result


if __name__ == "__main__":
    sys.exit(main())
