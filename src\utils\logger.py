"""
Logging System Module
Provides comprehensive logging functionality with file and console output
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional
import colorlog

try:
    from .config_manager import get_config_manager
except ImportError:
    from config_manager import get_config_manager


class LoggerManager:
    """
    Logger manager class for handling application logging
    Supports both file and console logging with configurable levels and formatting
    """
    
    def __init__(self, name: str = "ChinesePharmacoPDFSplitter"):
        """
        Initialize logger manager
        
        Args:
            name: Logger name
        """
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)  # Set to lowest level, handlers will filter
        
        # Prevent duplicate handlers
        if self.logger.handlers:
            self.logger.handlers.clear()
        
        self._setup_logging()
    
    def _setup_logging(self) -> None:
        """Setup logging configuration based on config file"""
        try:
            config_manager = get_config_manager()
            logging_config = config_manager.get_logging_config()
            
            # Setup file logging
            if logging_config.file_enabled:
                self._setup_file_logging(logging_config)
            
            # Setup console logging
            if logging_config.console_enabled:
                self._setup_console_logging(logging_config)
                
        except Exception as e:
            # Fallback to basic console logging if config fails
            self._setup_fallback_logging()
            self.logger.error(f"Failed to setup logging from config: {e}")
    
    def _setup_file_logging(self, logging_config) -> None:
        """
        Setup file logging with rotation
        
        Args:
            logging_config: Logging configuration object
        """
        try:
            # Create logs directory if it doesn't exist
            log_dir = Path(logging_config.file_directory)
            log_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate log filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_filename = logging_config.filename_format.format(timestamp=timestamp)
            log_path = log_dir / log_filename
            
            # Create rotating file handler
            file_handler = logging.handlers.RotatingFileHandler(
                filename=str(log_path),
                maxBytes=logging_config.max_size_mb * 1024 * 1024,  # Convert MB to bytes
                backupCount=logging_config.backup_count,
                encoding='utf-8'
            )
            
            # Set file logging level
            file_level = getattr(logging, logging_config.level.upper(), logging.INFO)
            file_handler.setLevel(file_level)
            
            # Create file formatter
            file_formatter = logging.Formatter(
                fmt='%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            file_handler.setFormatter(file_formatter)
            
            # Add handler to logger
            self.logger.addHandler(file_handler)
            
        except Exception as e:
            print(f"Failed to setup file logging: {e}")
    
    def _setup_console_logging(self, logging_config) -> None:
        """
        Setup console logging with optional colors
        
        Args:
            logging_config: Logging configuration object
        """
        try:
            # Create console handler
            console_handler = logging.StreamHandler(sys.stdout)
            
            # Set console logging level
            console_level = getattr(logging, logging_config.level.upper(), logging.INFO)
            console_handler.setLevel(console_level)
            
            # Create formatter (colored or plain)
            if logging_config.console_colored:
                # Colored formatter
                console_formatter = colorlog.ColoredFormatter(
                    fmt='%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    datefmt='%H:%M:%S',
                    log_colors={
                        'DEBUG': 'cyan',
                        'INFO': 'green',
                        'WARNING': 'yellow',
                        'ERROR': 'red',
                        'CRITICAL': 'red,bg_white',
                    }
                )
            else:
                # Plain formatter
                console_formatter = logging.Formatter(
                    fmt='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                    datefmt='%H:%M:%S'
                )
            
            console_handler.setFormatter(console_formatter)
            
            # Add handler to logger
            self.logger.addHandler(console_handler)
            
        except Exception as e:
            print(f"Failed to setup console logging: {e}")
    
    def _setup_fallback_logging(self) -> None:
        """Setup basic fallback logging if configuration fails"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        formatter = logging.Formatter(
            fmt='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        )
        console_handler.setFormatter(formatter)
        
        self.logger.addHandler(console_handler)
    
    def get_logger(self) -> logging.Logger:
        """
        Get the configured logger instance
        
        Returns:
            logging.Logger: Configured logger instance
        """
        return self.logger
    
    def log_operation_start(self, operation: str, details: str = "") -> None:
        """
        Log the start of an operation
        
        Args:
            operation: Operation name
            details: Additional details
        """
        message = f"Starting operation: {operation}"
        if details:
            message += f" - {details}"
        self.logger.info(message)
    
    def log_operation_end(self, operation: str, success: bool = True, details: str = "") -> None:
        """
        Log the end of an operation
        
        Args:
            operation: Operation name
            success: Whether operation was successful
            details: Additional details
        """
        status = "completed successfully" if success else "failed"
        message = f"Operation {operation} {status}"
        if details:
            message += f" - {details}"
        
        if success:
            self.logger.info(message)
        else:
            self.logger.error(message)
    
    def log_progress(self, current: int, total: int, item_name: str = "item") -> None:
        """
        Log progress information
        
        Args:
            current: Current progress count
            total: Total count
            item_name: Name of items being processed
        """
        percentage = (current / total * 100) if total > 0 else 0
        self.logger.info(f"Progress: {current}/{total} {item_name}s processed ({percentage:.1f}%)")
    
    def log_chapter_processing(self, chapter_name: str, page_range: str, action: str) -> None:
        """
        Log chapter processing information
        
        Args:
            chapter_name: Name of the chapter
            page_range: Page range being processed
            action: Action being performed
        """
        self.logger.info(f"{action} chapter: '{chapter_name}' (pages {page_range})")
    
    def log_error_with_context(self, error: Exception, context: str) -> None:
        """
        Log error with context information
        
        Args:
            error: Exception that occurred
            context: Context where error occurred
        """
        self.logger.error(f"Error in {context}: {type(error).__name__}: {str(error)}")
    
    def log_file_operation(self, operation: str, file_path: str, success: bool = True) -> None:
        """
        Log file operation
        
        Args:
            operation: Type of file operation
            file_path: Path of file being operated on
            success: Whether operation was successful
        """
        status = "successful" if success else "failed"
        self.logger.info(f"File {operation} {status}: {file_path}")


# Global logger manager instance
_logger_manager: Optional[LoggerManager] = None


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    Get logger instance
    
    Args:
        name: Logger name (optional)
        
    Returns:
        logging.Logger: Logger instance
    """
    global _logger_manager
    if _logger_manager is None:
        logger_name = name or "ChinesePharmacoPDFSplitter"
        _logger_manager = LoggerManager(logger_name)
    
    return _logger_manager.get_logger()


def get_logger_manager() -> LoggerManager:
    """
    Get logger manager instance
    
    Returns:
        LoggerManager: Logger manager instance
    """
    global _logger_manager
    if _logger_manager is None:
        _logger_manager = LoggerManager()
    
    return _logger_manager
