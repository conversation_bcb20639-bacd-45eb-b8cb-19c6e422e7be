"""
Configuration Manager Mo<PERSON>le
Handles loading and managing application configuration from YAML files
"""

import yaml
import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class AppConfig:
    """Application configuration data class"""
    name: str
    version: str
    author: str


@dataclass
class PDFConfig:
    """PDF processing configuration data class"""
    volumes: list
    font_size_threshold: float
    confidence_threshold: float
    chapter_keywords: list
    use_chinese_names: bool
    include_page_numbers: bool
    include_timestamp: bool
    timestamp_format: str


@dataclass
class GUIConfig:
    """GUI configuration data class"""
    window_width: int
    window_height: int
    window_title: str
    progress_update_interval: int
    show_current_chapter: bool
    show_page_progress: bool


@dataclass
class LoggingConfig:
    """Logging configuration data class"""
    level: str
    file_enabled: bool
    file_directory: str
    filename_format: str
    max_size_mb: int
    backup_count: int
    console_enabled: bool
    console_colored: bool


@dataclass
class DirectoriesConfig:
    """Directories configuration data class"""
    output: str
    temp: str
    logs: str
    tests: str


@dataclass
class ErrorHandlingConfig:
    """Error handling configuration data class"""
    max_retries: int
    operation_timeout: int
    continue_on_error: bool


class ConfigManager:
    """
    Configuration manager class for handling application settings
    Loads configuration from YAML file and provides typed access to settings
    """
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize configuration manager
        
        Args:
            config_path: Path to configuration file. If None, uses default path.
        """
        if config_path is None:
            # Default config path relative to project root
            project_root = Path(__file__).parent.parent.parent
            config_path = project_root / "config" / "config.yaml"
        
        self.config_path = Path(config_path)
        self._config_data: Dict[str, Any] = {}
        self._load_config()
    
    def _load_config(self) -> None:
        """Load configuration from YAML file"""
        try:
            if not self.config_path.exists():
                raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self._config_data = yaml.safe_load(file)
                
        except Exception as e:
            raise RuntimeError(f"Failed to load configuration: {e}")
    
    def reload_config(self) -> None:
        """Reload configuration from file"""
        self._load_config()
    
    def get_app_config(self) -> AppConfig:
        """Get application configuration"""
        app_data = self._config_data.get('app', {})
        return AppConfig(
            name=app_data.get('name', 'Chinese Pharmacopoeia PDF Splitter'),
            version=app_data.get('version', '1.0.0'),
            author=app_data.get('author', 'PDF Processing Tool')
        )
    
    def get_pdf_config(self) -> PDFConfig:
        """Get PDF processing configuration"""
        pdf_data = self._config_data.get('pdf', {})
        chapter_detection = pdf_data.get('chapter_detection', {})
        output_settings = pdf_data.get('output', {})
        
        return PDFConfig(
            volumes=pdf_data.get('volumes', ['Volume 1', 'Volume 2', 'Volume 3', 'Volume 4']),
            font_size_threshold=chapter_detection.get('font_size_threshold', 1.2),
            confidence_threshold=chapter_detection.get('confidence_threshold', 0.8),
            chapter_keywords=chapter_detection.get('chapter_keywords', ['第', '章', '部分', '篇']),
            use_chinese_names=output_settings.get('use_chinese_names', True),
            include_page_numbers=output_settings.get('include_page_numbers', True),
            include_timestamp=output_settings.get('include_timestamp', True),
            timestamp_format=output_settings.get('timestamp_format', '%Y%m%d%H%M')
        )
    
    def get_gui_config(self) -> GUIConfig:
        """Get GUI configuration"""
        gui_data = self._config_data.get('gui', {})
        window_data = gui_data.get('window', {})
        progress_data = gui_data.get('progress', {})
        
        return GUIConfig(
            window_width=window_data.get('width', 800),
            window_height=window_data.get('height', 600),
            window_title=window_data.get('title', 'Chinese Pharmacopoeia PDF Splitter'),
            progress_update_interval=progress_data.get('update_interval', 100),
            show_current_chapter=progress_data.get('show_current_chapter', True),
            show_page_progress=progress_data.get('show_page_progress', True)
        )
    
    def get_logging_config(self) -> LoggingConfig:
        """Get logging configuration"""
        logging_data = self._config_data.get('logging', {})
        file_data = logging_data.get('file', {})
        console_data = logging_data.get('console', {})
        
        return LoggingConfig(
            level=logging_data.get('level', 'INFO'),
            file_enabled=file_data.get('enabled', True),
            file_directory=file_data.get('directory', 'logs'),
            filename_format=file_data.get('filename_format', 'log_{timestamp}.log'),
            max_size_mb=file_data.get('max_size_mb', 10),
            backup_count=file_data.get('backup_count', 5),
            console_enabled=console_data.get('enabled', True),
            console_colored=console_data.get('colored', True)
        )
    
    def get_directories_config(self) -> DirectoriesConfig:
        """Get directories configuration"""
        dirs_data = self._config_data.get('directories', {})
        
        return DirectoriesConfig(
            output=dirs_data.get('output', 'output'),
            temp=dirs_data.get('temp', 'temp'),
            logs=dirs_data.get('logs', 'logs'),
            tests=dirs_data.get('tests', 'tests/test_data')
        )
    
    def get_error_handling_config(self) -> ErrorHandlingConfig:
        """Get error handling configuration"""
        error_data = self._config_data.get('error_handling', {})
        
        return ErrorHandlingConfig(
            max_retries=error_data.get('max_retries', 3),
            operation_timeout=error_data.get('operation_timeout', 30),
            continue_on_error=error_data.get('continue_on_error', True)
        )
    
    def get_raw_config(self) -> Dict[str, Any]:
        """Get raw configuration data"""
        return self._config_data.copy()
    
    def update_config(self, section: str, key: str, value: Any) -> None:
        """
        Update configuration value
        
        Args:
            section: Configuration section name
            key: Configuration key
            value: New value
        """
        if section not in self._config_data:
            self._config_data[section] = {}
        
        self._config_data[section][key] = value
    
    def save_config(self) -> None:
        """Save current configuration to file"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(self._config_data, file, default_flow_style=False, 
                         allow_unicode=True, indent=2)
        except Exception as e:
            raise RuntimeError(f"Failed to save configuration: {e}")


# Global configuration manager instance
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """
    Get global configuration manager instance
    
    Returns:
        ConfigManager: Global configuration manager instance
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def reload_config() -> None:
    """Reload global configuration"""
    global _config_manager
    if _config_manager is not None:
        _config_manager.reload_config()
