"""
Chapter Information Data Models
Defines data structures for storing chapter information extracted from PDFs
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from enum import Enum
import json


class VolumeType(Enum):
    """Enumeration for Chinese Pharmacopoeia volume types"""
    VOLUME_1 = "Volume 1"
    VOLUME_2 = "Volume 2" 
    VOLUME_3 = "Volume 3"
    VOLUME_4 = "Volume 4"


class ChapterType(Enum):
    """Enumeration for chapter types"""
    MAIN_CHAPTER = "main_chapter"
    SUB_CHAPTER = "sub_chapter"
    APPENDIX = "appendix"
    INDEX = "index"


@dataclass
class PageInfo:
    """Information about a specific page"""
    page_number: int
    has_chapter_title: bool = False
    chapter_titles: List[str] = field(default_factory=list)
    confidence_scores: List[float] = field(default_factory=list)
    
    def add_chapter_title(self, title: str, confidence: float = 1.0) -> None:
        """
        Add a chapter title found on this page
        
        Args:
            title: Chapter title text
            confidence: Confidence score for title detection
        """
        self.chapter_titles.append(title)
        self.confidence_scores.append(confidence)
        self.has_chapter_title = True


@dataclass
class ChapterInfo:
    """Information about a chapter extracted from PDF"""
    title: str
    start_page: int
    end_page: Optional[int] = None
    chapter_type: ChapterType = ChapterType.MAIN_CHAPTER
    confidence_score: float = 1.0
    volume: Optional[VolumeType] = None
    
    # Additional metadata
    font_size: Optional[float] = None
    font_name: Optional[str] = None
    text_position: Optional[Dict[str, float]] = None
    
    # Processing information
    processed: bool = False
    output_filename: Optional[str] = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        """Post-initialization validation"""
        if self.start_page <= 0:
            raise ValueError("Start page must be positive")
        
        if self.end_page is not None and self.end_page < self.start_page:
            raise ValueError("End page must be greater than or equal to start page")
    
    @property
    def page_count(self) -> int:
        """Get the number of pages in this chapter"""
        if self.end_page is None:
            return 1
        return self.end_page - self.start_page + 1
    
    @property
    def page_range_str(self) -> str:
        """Get page range as string"""
        if self.end_page is None:
            return str(self.start_page)
        return f"{self.start_page}-{self.end_page}"
    
    def set_end_page(self, end_page: int) -> None:
        """
        Set the end page for this chapter
        
        Args:
            end_page: Last page of the chapter
        """
        if end_page < self.start_page:
            raise ValueError("End page must be greater than or equal to start page")
        self.end_page = end_page
    
    def generate_filename(self, use_chinese: bool = True, include_pages: bool = True, 
                         include_timestamp: bool = True, timestamp: str = "") -> str:
        """
        Generate output filename for this chapter
        
        Args:
            use_chinese: Whether to use Chinese characters in filename
            include_pages: Whether to include page numbers in filename
            include_timestamp: Whether to include timestamp in filename
            timestamp: Timestamp string to use
            
        Returns:
            str: Generated filename
        """
        # Clean title for filename (remove invalid characters)
        clean_title = self._clean_filename(self.title)
        
        # Convert to English if requested (simplified conversion)
        if not use_chinese:
            clean_title = self._convert_to_english(clean_title)
        
        filename_parts = [clean_title]
        
        # Add page range if requested
        if include_pages:
            filename_parts.append(self.page_range_str)
        
        # Add timestamp if requested
        if include_timestamp and timestamp:
            filename_parts.append(timestamp)
        
        filename = "_".join(filename_parts) + ".pdf"
        self.output_filename = filename
        return filename
    
    def _clean_filename(self, filename: str) -> str:
        """
        Clean filename by removing invalid characters
        
        Args:
            filename: Original filename
            
        Returns:
            str: Cleaned filename
        """
        # Remove or replace invalid filename characters
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        
        # Remove extra spaces and trim
        filename = ' '.join(filename.split())
        return filename.strip()
    
    def _convert_to_english(self, chinese_text: str) -> str:
        """
        Simple conversion of Chinese text to English (placeholder)
        In a real implementation, this could use translation services
        
        Args:
            chinese_text: Chinese text to convert
            
        Returns:
            str: English text (simplified conversion)
        """
        # This is a simplified conversion - in practice you might want
        # to use a translation service or maintain a mapping dictionary
        conversions = {
            '第': 'Chapter',
            '章': '',
            '部分': 'Part',
            '篇': 'Section',
            '附录': 'Appendix',
            '索引': 'Index'
        }
        
        result = chinese_text
        for chinese, english in conversions.items():
            result = result.replace(chinese, english)
        
        return result.strip()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert chapter info to dictionary"""
        return {
            'title': self.title,
            'start_page': self.start_page,
            'end_page': self.end_page,
            'chapter_type': self.chapter_type.value,
            'confidence_score': self.confidence_score,
            'volume': self.volume.value if self.volume else None,
            'font_size': self.font_size,
            'font_name': self.font_name,
            'text_position': self.text_position,
            'processed': self.processed,
            'output_filename': self.output_filename,
            'error_message': self.error_message,
            'page_count': self.page_count,
            'page_range_str': self.page_range_str
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ChapterInfo':
        """Create ChapterInfo from dictionary"""
        chapter_info = cls(
            title=data['title'],
            start_page=data['start_page'],
            end_page=data.get('end_page'),
            chapter_type=ChapterType(data.get('chapter_type', ChapterType.MAIN_CHAPTER.value)),
            confidence_score=data.get('confidence_score', 1.0),
            volume=VolumeType(data['volume']) if data.get('volume') else None,
            font_size=data.get('font_size'),
            font_name=data.get('font_name'),
            text_position=data.get('text_position'),
            processed=data.get('processed', False),
            output_filename=data.get('output_filename'),
            error_message=data.get('error_message')
        )
        return chapter_info


@dataclass
class PDFAnalysisResult:
    """Result of PDF analysis containing all detected chapters"""
    pdf_path: str
    volume: VolumeType
    total_pages: int
    chapters: List[ChapterInfo] = field(default_factory=list)
    analysis_timestamp: Optional[str] = None
    analysis_errors: List[str] = field(default_factory=list)
    
    def add_chapter(self, chapter: ChapterInfo) -> None:
        """Add a chapter to the analysis result"""
        chapter.volume = self.volume
        self.chapters.append(chapter)
    
    def get_chapters_by_type(self, chapter_type: ChapterType) -> List[ChapterInfo]:
        """Get chapters filtered by type"""
        return [ch for ch in self.chapters if ch.chapter_type == chapter_type]
    
    def get_chapter_count(self) -> int:
        """Get total number of chapters"""
        return len(self.chapters)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert analysis result to dictionary"""
        return {
            'pdf_path': self.pdf_path,
            'volume': self.volume.value,
            'total_pages': self.total_pages,
            'chapters': [ch.to_dict() for ch in self.chapters],
            'analysis_timestamp': self.analysis_timestamp,
            'analysis_errors': self.analysis_errors,
            'chapter_count': self.get_chapter_count()
        }
    
    def save_to_json(self, output_path: str) -> None:
        """Save analysis result to JSON file"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
    
    @classmethod
    def load_from_json(cls, json_path: str) -> 'PDFAnalysisResult':
        """Load analysis result from JSON file"""
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        result = cls(
            pdf_path=data['pdf_path'],
            volume=VolumeType(data['volume']),
            total_pages=data['total_pages'],
            analysis_timestamp=data.get('analysis_timestamp'),
            analysis_errors=data.get('analysis_errors', [])
        )
        
        # Load chapters
        for ch_data in data.get('chapters', []):
            chapter = ChapterInfo.from_dict(ch_data)
            result.add_chapter(chapter)
        
        return result
