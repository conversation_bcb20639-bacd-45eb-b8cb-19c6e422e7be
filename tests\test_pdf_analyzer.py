"""
Unit tests for PDF Analyzer module
"""

import unittest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add src to path for imports
import sys
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from core.pdf_analyzer import PDFAnalyzer
from models.chapter_info import VolumeType, ChapterType, ChapterInfo, PDFAnalysisResult


class TestPDFAnalyzer(unittest.TestCase):
    """Test cases for PDFAnalyzer class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.analyzer = PDFAnalyzer()
        self.test_pdf_path = "test_document.pdf"
        self.test_volume = VolumeType.VOLUME_1
    
    def test_init(self):
        """Test PDFAnalyzer initialization"""
        self.assertIsNotNone(self.analyzer.config_manager)
        self.assertIsNotNone(self.analyzer.pdf_config)
        self.assertIsNotNone(self.analyzer.logger)
        self.assertIsNotNone(self.analyzer.chapter_patterns)
        self.assertGreater(len(self.analyzer.chapter_patterns), 0)
    
    def test_build_chapter_patterns(self):
        """Test chapter pattern building"""
        patterns = self.analyzer._build_chapter_patterns()
        
        self.assertIsInstance(patterns, list)
        self.assertGreater(len(patterns), 0)
        
        # Test that patterns can match expected chapter titles
        test_titles = [
            "第一章 总则",
            "第二部分 药品质量标准",
            "附录A 测试方法",
            "索引"
        ]
        
        for title in test_titles:
            matched = any(pattern.match(title) for pattern in patterns)
            self.assertTrue(matched, f"No pattern matched title: {title}")
    
    def test_detect_chapter_title(self):
        """Test chapter title detection"""
        # Test valid chapter title
        chapter_info = self.analyzer._detect_chapter_title(
            "第一章 总则", 14.0, "Arial-Bold", 1
        )
        
        self.assertIsNotNone(chapter_info)
        self.assertEqual(chapter_info.title, "第一章 总则")
        self.assertEqual(chapter_info.start_page, 1)
        self.assertGreaterEqual(chapter_info.confidence_score, 0.8)
        
        # Test invalid chapter title
        chapter_info = self.analyzer._detect_chapter_title(
            "普通文本内容", 12.0, "Arial", 1
        )
        
        self.assertIsNone(chapter_info)
    
    def test_calculate_confidence(self):
        """Test confidence calculation"""
        # Test high confidence title
        confidence = self.analyzer._calculate_confidence("第一章 总则", 16.0, "Arial-Bold")
        self.assertGreaterEqual(confidence, 0.8)
        
        # Test medium confidence title
        confidence = self.analyzer._calculate_confidence("附录A", 12.0, "Arial")
        self.assertGreaterEqual(confidence, 0.5)
        self.assertLess(confidence, 0.8)
        
        # Test low confidence title
        confidence = self.analyzer._calculate_confidence("普通文本", 10.0, "Arial")
        self.assertLessEqual(confidence, 0.5)
    
    def test_determine_chapter_type(self):
        """Test chapter type determination"""
        # Test main chapter
        chapter_type = self.analyzer._determine_chapter_type("第一章 总则")
        self.assertEqual(chapter_type, ChapterType.MAIN_CHAPTER)
        
        # Test appendix
        chapter_type = self.analyzer._determine_chapter_type("附录A 测试方法")
        self.assertEqual(chapter_type, ChapterType.APPENDIX)
        
        # Test index
        chapter_type = self.analyzer._determine_chapter_type("索引")
        self.assertEqual(chapter_type, ChapterType.INDEX)
        
        # Test sub chapter
        chapter_type = self.analyzer._determine_chapter_type("1.1 基本要求")
        self.assertEqual(chapter_type, ChapterType.SUB_CHAPTER)
    
    def test_is_potential_chapter_title(self):
        """Test potential chapter title detection"""
        # Test valid titles
        valid_titles = [
            "第一章 总则",
            "附录A",
            "索引",
            "1. 基本要求"
        ]
        
        for title in valid_titles:
            self.assertTrue(
                self.analyzer._is_potential_chapter_title(title),
                f"Should be potential chapter title: {title}"
            )
        
        # Test invalid titles
        invalid_titles = [
            "",
            "a",
            "普通段落文本内容"
        ]
        
        for title in invalid_titles:
            self.assertFalse(
                self.analyzer._is_potential_chapter_title(title),
                f"Should not be potential chapter title: {title}"
            )
    
    def test_titles_similar(self):
        """Test title similarity comparison"""
        # Test similar titles
        self.assertTrue(
            self.analyzer._titles_similar("第一章 总则", "第一章总则")
        )
        
        self.assertTrue(
            self.analyzer._titles_similar("附录A 测试", "附录A测试")
        )
        
        # Test dissimilar titles
        self.assertFalse(
            self.analyzer._titles_similar("第一章 总则", "第二章 药品")
        )
    
    def test_find_existing_chapter(self):
        """Test finding existing chapters"""
        chapters = [
            ChapterInfo("第一章 总则", 1),
            ChapterInfo("第二章 药品", 10),
        ]
        
        # Test finding existing chapter
        found = self.analyzer._find_existing_chapter(chapters, "第一章总则", 1)
        self.assertIsNotNone(found)
        self.assertEqual(found.title, "第一章 总则")
        
        # Test not finding non-existing chapter
        found = self.analyzer._find_existing_chapter(chapters, "第三章", 20)
        self.assertIsNone(found)
    
    def test_post_process_chapters(self):
        """Test chapter post-processing"""
        # Create test analysis result
        result = PDFAnalysisResult(
            pdf_path="test.pdf",
            volume=VolumeType.VOLUME_1,
            total_pages=100
        )
        
        # Add chapters in random order
        chapters = [
            ChapterInfo("第二章 药品", 20),
            ChapterInfo("第一章 总则", 1),
            ChapterInfo("第三章 检验", 50),
        ]
        
        for chapter in chapters:
            result.add_chapter(chapter)
        
        # Post-process
        processed_result = self.analyzer._post_process_chapters(result)
        
        # Check that chapters are sorted by start page
        self.assertEqual(processed_result.chapters[0].title, "第一章 总则")
        self.assertEqual(processed_result.chapters[1].title, "第二章 药品")
        self.assertEqual(processed_result.chapters[2].title, "第三章 检验")
        
        # Check that end pages are set correctly
        self.assertEqual(processed_result.chapters[0].end_page, 19)
        self.assertEqual(processed_result.chapters[1].end_page, 49)
        self.assertEqual(processed_result.chapters[2].end_page, 100)
    
    @patch('fitz.open')
    def test_analyze_pdf_file_not_found(self, mock_fitz_open):
        """Test analyze_pdf with non-existent file"""
        with self.assertRaises(FileNotFoundError):
            self.analyzer.analyze_pdf("non_existent.pdf", VolumeType.VOLUME_1)
    
    def test_save_and_load_analysis_result(self):
        """Test saving and loading analysis results"""
        # Create test result
        result = PDFAnalysisResult(
            pdf_path="test.pdf",
            volume=VolumeType.VOLUME_1,
            total_pages=100
        )
        
        chapter = ChapterInfo("第一章 总则", 1, 10)
        result.add_chapter(chapter)
        
        # Test saving
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            self.analyzer.save_analysis_result(result, temp_path)
            self.assertTrue(Path(temp_path).exists())
            
            # Test loading
            loaded_result = self.analyzer.load_analysis_result(temp_path)
            
            self.assertEqual(loaded_result.pdf_path, result.pdf_path)
            self.assertEqual(loaded_result.volume, result.volume)
            self.assertEqual(loaded_result.total_pages, result.total_pages)
            self.assertEqual(len(loaded_result.chapters), 1)
            self.assertEqual(loaded_result.chapters[0].title, "第一章 总则")
            
        finally:
            # Clean up
            if Path(temp_path).exists():
                os.unlink(temp_path)


class TestChapterDetectionIntegration(unittest.TestCase):
    """Integration tests for chapter detection"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.analyzer = PDFAnalyzer()
    
    def test_chapter_detection_patterns(self):
        """Test various chapter detection patterns"""
        test_cases = [
            ("第一章 总则", True, ChapterType.MAIN_CHAPTER),
            ("附录A 高效液相色谱法", True, ChapterType.APPENDIX),
            ("附录1 测试方法", True, ChapterType.APPENDIX),
            ("索引", True, ChapterType.INDEX),
            ("1. 基本要求", True, ChapterType.SUB_CHAPTER),
            ("普通文本内容", False, None),
            ("", False, None),
        ]
        
        for text, should_detect, expected_type in test_cases:
            chapter_info = self.analyzer._detect_chapter_title(text, 14.0, "Arial-Bold", 1)

            if should_detect:
                self.assertIsNotNone(chapter_info, f"Should detect chapter: {text}")
                if expected_type:
                    self.assertEqual(
                        chapter_info.chapter_type, expected_type,
                        f"Wrong chapter type for: {text}"
                    )
            else:
                self.assertIsNone(chapter_info, f"Should not detect chapter: {text}")


if __name__ == '__main__':
    unittest.main()
