#!/usr/bin/env python3
"""
Test script for Chinese Pharmacopoeia PDF Splitter
Quick test to verify basic functionality
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_imports():
    """Test that all modules can be imported"""
    print("Testing imports...")
    
    try:
        # Test configuration
        from utils.config_manager import get_config_manager
        config_manager = get_config_manager()
        print("✓ Configuration manager imported successfully")
        
        # Test logger
        from utils.logger import get_logger
        logger = get_logger()
        print("✓ Logger imported successfully")
        
        # Test file manager
        from utils.file_utils import get_file_manager
        file_manager = get_file_manager()
        print("✓ File manager imported successfully")
        
        # Test models
        from models.chapter_info import ChapterInfo, VolumeType
        print("✓ Data models imported successfully")
        
        # Test core modules
        from core.pdf_analyzer import PDFAnalyzer
        from core.pdf_splitter import PDFSplitter
        print("✓ Core modules imported successfully")
        
        # Test GUI (this might fail if PyQt6 is not properly installed)
        try:
            from gui.main_window import create_main_window
            print("✓ GUI modules imported successfully")
        except ImportError as e:
            print(f"⚠ GUI import failed (this is expected if PyQt6 is not installed): {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False

def test_configuration():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        from utils.config_manager import get_config_manager
        
        config_manager = get_config_manager()
        
        # Test different config sections
        app_config = config_manager.get_app_config()
        print(f"✓ App config loaded: {app_config.name} v{app_config.version}")
        
        pdf_config = config_manager.get_pdf_config()
        print(f"✓ PDF config loaded: {len(pdf_config.volumes)} volumes supported")
        
        gui_config = config_manager.get_gui_config()
        print(f"✓ GUI config loaded: {gui_config.window_width}x{gui_config.window_height}")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False

def test_logging():
    """Test logging functionality"""
    print("\nTesting logging...")
    
    try:
        from utils.logger import get_logger
        
        logger = get_logger()
        
        # Test different log levels
        logger.debug("Debug message test")
        logger.info("Info message test")
        logger.warning("Warning message test")
        
        print("✓ Logging test completed (check logs directory for output)")
        return True
        
    except Exception as e:
        print(f"✗ Logging test failed: {e}")
        return False

def test_file_operations():
    """Test file operations"""
    print("\nTesting file operations...")
    
    try:
        from utils.file_utils import get_file_manager
        
        file_manager = get_file_manager()
        
        # Test directory creation
        test_dir = "test_temp"
        if file_manager.ensure_directory(test_dir):
            print("✓ Directory creation test passed")
            
            # Clean up
            import shutil
            shutil.rmtree(test_dir, ignore_errors=True)
        else:
            print("✗ Directory creation test failed")
            return False
        
        # Test file listing
        current_files = file_manager.list_files(".", "*.py")
        print(f"✓ File listing test passed: found {len(current_files)} Python files")
        
        return True
        
    except Exception as e:
        print(f"✗ File operations test failed: {e}")
        return False

def test_data_models():
    """Test data models"""
    print("\nTesting data models...")
    
    try:
        from models.chapter_info import ChapterInfo, VolumeType, ChapterType
        
        # Test chapter info creation
        chapter = ChapterInfo(
            title="第一章 测试章节",
            start_page=1,
            end_page=10,
            volume=VolumeType.VOLUME_1,
            chapter_type=ChapterType.MAIN_CHAPTER
        )
        
        print(f"✓ Chapter info created: {chapter.title} ({chapter.page_range_str})")
        
        # Test filename generation
        filename = chapter.generate_filename(
            use_chinese=True,
            include_pages=True,
            include_timestamp=True,
            timestamp="202509241142"
        )
        
        print(f"✓ Filename generation test: {filename}")
        
        return True
        
    except Exception as e:
        print(f"✗ Data models test failed: {e}")
        return False

def check_pdf_files():
    """Check for available PDF files for testing"""
    print("\nChecking for PDF files...")
    
    pdf_files = []
    for file_path in Path(".").glob("*.pdf"):
        if file_path.is_file():
            pdf_files.append(str(file_path))
    
    if pdf_files:
        print(f"✓ Found {len(pdf_files)} PDF files for testing:")
        for pdf_file in pdf_files:
            file_size = Path(pdf_file).stat().st_size / (1024 * 1024)  # MB
            print(f"  - {pdf_file} ({file_size:.1f} MB)")
    else:
        print("⚠ No PDF files found in current directory")
    
    return pdf_files

def main():
    """Run all tests"""
    print("Chinese Pharmacopoeia PDF Splitter - Test Suite")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_configuration,
        test_logging,
        test_file_operations,
        test_data_models
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test_func.__name__} crashed: {e}")
    
    # Check for PDF files
    check_pdf_files()
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application should be ready to run.")
        print("\nTo start the application, run:")
        print("  python run.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
