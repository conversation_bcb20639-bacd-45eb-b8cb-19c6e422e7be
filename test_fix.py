#!/usr/bin/env python3
"""
Test script to verify the PDF analysis fix
Tests the edge case handling for overlapping chapters
"""

import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from models.chapter_info import ChapterInfo, PDFAnalysisResult, VolumeType
from core.pdf_analyzer import PDFAnalyzer


def test_chapter_overlap_handling():
    """Test handling of overlapping chapters"""
    print("Testing chapter overlap handling...")
    
    analyzer = PDFAnalyzer()
    
    # Create test analysis result with overlapping chapters
    result = PDFAnalysisResult(
        pdf_path="test.pdf",
        volume=VolumeType.VOLUME_1,
        total_pages=100
    )
    
    # Add chapters that might overlap (same page or adjacent pages)
    test_chapters = [
        ChapterInfo("第一章 总则", 1),
        ChapterInfo("第二章 药品", 1),  # Same page as first chapter
        ChapterInfo("第三章 检验", 2),  # Adjacent page
        ChapterInfo("第四章 标准", 5),
        ChapterInfo("附录A", 50),
        ChapterInfo("附录B", 50),  # Same page as previous
        ChapterInfo("索引", 90),
    ]
    
    for chapter in test_chapters:
        result.add_chapter(chapter)
    
    print(f"Before post-processing: {len(result.chapters)} chapters")
    for chapter in result.chapters:
        print(f"  - {chapter.title}: page {chapter.start_page}")
    
    # Test post-processing
    try:
        processed_result = analyzer._post_process_chapters(result)
        
        print(f"\nAfter post-processing: {len(processed_result.chapters)} chapters")
        for chapter in processed_result.chapters:
            print(f"  - {chapter.title}: pages {chapter.page_range_str} ({chapter.page_count} pages)")
        
        print("\n✅ Post-processing completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Post-processing failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_chapter_info_edge_cases():
    """Test ChapterInfo edge cases"""
    print("\nTesting ChapterInfo edge cases...")
    
    try:
        # Test normal case
        chapter1 = ChapterInfo("Test Chapter", 10)
        chapter1.set_end_page(20)
        print(f"✅ Normal case: {chapter1.page_range_str}")
        
        # Test edge case: end page equals start page
        chapter2 = ChapterInfo("Single Page Chapter", 15)
        chapter2.set_end_page(15)
        print(f"✅ Single page case: {chapter2.page_range_str}")
        
        # Test edge case: end page less than start page (should be handled gracefully)
        chapter3 = ChapterInfo("Problematic Chapter", 20)
        chapter3.set_end_page(15)  # This should be adjusted to start page
        print(f"✅ Adjusted case: {chapter3.page_range_str}")
        
        return True
        
    except Exception as e:
        print(f"❌ ChapterInfo test failed: {e}")
        return False


def main():
    """Main test function"""
    print("PDF Analysis Fix Verification")
    print("=" * 40)
    
    test1_passed = test_chapter_overlap_handling()
    test2_passed = test_chapter_info_edge_cases()
    
    print("\n" + "=" * 40)
    print("Test Results:")
    print(f"  Chapter overlap handling: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"  ChapterInfo edge cases: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! The fix should resolve the PDF analysis error.")
        print("\nYou can now try running the application again:")
        print("  python run.py")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the error messages above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
