# Chinese Pharmacopoeia PDF Splitter

A Python application for analyzing and splitting Chinese Pharmacopoeia 2020 Edition PDF files by chapters.

## Features

- **PDF Analysis**: Automatically detect chapter titles and page numbers from Chinese Pharmacopoeia PDFs
- **Chapter-based Splitting**: Split PDF files into individual chapters with customizable naming
- **Multi-volume Support**: Support for all 4 volumes of Chinese Pharmacopoeia 2020 Edition
- **GUI Interface**: User-friendly PyQt6 interface with progress tracking
- **Flexible Naming**: Configurable output file naming (Chinese/English, with/without page numbers and timestamps)
- **Process Control**: Pause, resume, and stop splitting operations
- **Comprehensive Logging**: Detailed logging with timestamped log files
- **Error Handling**: Robust error handling and recovery mechanisms

## Project Structure

```
chinese_pharmacopoeia_splitter/
├── src/                          # Source code
│   ├── gui/                      # GUI modules
│   ├── core/                     # Core functionality
│   ├── utils/                    # Utility modules
│   └── models/                   # Data models
├── config/                       # Configuration files
├── logs/                         # Log files
├── tests/                        # Unit tests
├── output/                       # Split PDF output
├── temp/                         # Temporary files
└── requirements.txt              # Dependencies
```

## Installation

1. Clone or download the project
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

1. Run the application:
   ```bash
   python run.py
   ```

2. Select the Chinese Pharmacopoeia PDF file
3. Choose the volume (1-4)
4. Configure output naming preferences
5. Start the splitting process

## Configuration

Edit `config/config.yaml` to customize:
- Chapter detection parameters
- Output file naming
- GUI settings
- Logging configuration

## Testing

Run unit tests:
```bash
pytest tests/
```

## Requirements

- Python 3.8+
- Windows 11
- PyQt6
- PDF processing libraries (PyPDF2, PyMuPDF, pdfplumber)

## License

This project is for internal use and research purposes.

## Development Notes

- Uses functional programming approach
- Comprehensive English comments
- Configuration-driven design
- Modular architecture for maintainability

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run tests to verify installation:**
   ```bash
   python test_app.py
   ```

3. **Run demo analysis:**
   ```bash
   python demo_analysis.py
   ```

4. **Start the GUI application:**
   ```bash
   python run.py
   ```

## Usage Guide

### Step 1: Select PDF File
- Click "Browse..." to select your Chinese Pharmacopoeia PDF file
- Choose the appropriate volume (1-4) from the dropdown

### Step 2: Configure Output Options
- **Use Chinese characters**: Keep Chinese names in output files
- **Include page numbers**: Add page ranges to filenames
- **Include timestamp**: Add timestamp to avoid filename conflicts

### Step 3: Analyze PDF
- Click "Analyze PDF" to detect chapters
- The application will scan the PDF and identify chapter boundaries
- Progress will be shown in the log area

### Step 4: Split PDF
- After analysis, click "Split PDF" to begin splitting
- Use Pause/Resume/Cancel buttons to control the process
- Progress bar shows current status
- Output files will be saved to the specified directory

## File Naming Convention

Output files follow this pattern:
```
[Chapter_Title]_[Page_Range]_[Timestamp].pdf
```

Examples:
- `第一章 总则_1-20_202509241142.pdf`
- `Chapter General Rules_1-20_202509241142.pdf` (English mode)

## Troubleshooting

### Common Issues

1. **Import errors**: Ensure all dependencies are installed
   ```bash
   pip install -r requirements.txt
   ```

2. **PDF analysis fails**: Check that the PDF file is not corrupted and is a valid Chinese Pharmacopoeia document

3. **GUI doesn't start**: Verify PyQt6 is properly installed
   ```bash
   pip install PyQt6 PyQt6-tools
   ```

4. **Memory issues with large PDFs**: The application handles large files, but very large PDFs (>500MB) may require more system memory

### Log Files

Check the `logs/` directory for detailed error information. Log files are named with timestamps for easy identification.

## Technical Details

### Supported PDF Features
- Text extraction using PyMuPDF and pdfplumber
- Font size and style analysis for chapter detection
- Multi-language support (Chinese and English)
- Robust error handling and recovery

### Chapter Detection Algorithm
1. **Pattern Matching**: Uses regex patterns to identify chapter titles
2. **Font Analysis**: Considers font size and style for confidence scoring
3. **Context Analysis**: Validates detected titles against expected patterns
4. **Post-processing**: Sorts chapters and assigns page ranges

### Performance Considerations
- Large PDFs are processed in chunks to manage memory usage
- Background processing prevents GUI freezing
- Progress tracking provides user feedback
- Pause/resume functionality for long operations
