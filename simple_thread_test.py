#!/usr/bin/env python3
"""
Simple test to verify thread management improvements
"""

import sys
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def test_imports():
    """Test that all imports work correctly"""
    print("Testing imports...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from PyQt6.QtCore import QThread
        print("✅ PyQt6 imports successful")
        
        from gui.main_window import AnalysisWorker, SplittingWorker, MainWindow
        print("✅ GUI module imports successful")
        
        from models.chapter_info import VolumeType
        print("✅ Model imports successful")
        
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False

def test_thread_classes():
    """Test that thread classes can be instantiated"""
    print("\nTesting thread class instantiation...")
    
    try:
        from gui.main_window import AnalysisWorker, SplittingWorker
        from models.chapter_info import VolumeType, PDFAnalysisResult
        
        # Test AnalysisWorker instantiation
        worker1 = AnalysisWorker("test.pdf", VolumeType.VOLUME_1)
        print("✅ AnalysisWorker instantiation successful")
        
        # Test SplittingWorker instantiation
        result = PDFAnalysisResult("test.pdf", VolumeType.VOLUME_1, 10)
        worker2 = SplittingWorker(result, "output", True, True, True)
        print("✅ SplittingWorker instantiation successful")
        
        return True
    except Exception as e:
        print(f"❌ Thread class test failed: {e}")
        return False

def test_gui_creation():
    """Test that GUI can be created without errors"""
    print("\nTesting GUI creation...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        from gui.main_window import create_main_window
        
        # Create QApplication (required for GUI)
        app = QApplication([])
        
        # Create main window
        window = create_main_window()
        print("✅ Main window creation successful")
        
        # Clean up
        app.quit()
        return True
    except Exception as e:
        print(f"❌ GUI creation failed: {e}")
        return False

def main():
    """Main test function"""
    print("Simple Thread Management Verification")
    print("=" * 40)
    
    test1 = test_imports()
    test2 = test_thread_classes()
    test3 = test_gui_creation()
    
    print("\n" + "=" * 40)
    print("Test Results:")
    print(f"  Imports: {'✅ PASSED' if test1 else '❌ FAILED'}")
    print(f"  Thread classes: {'✅ PASSED' if test2 else '❌ FAILED'}")
    print(f"  GUI creation: {'✅ PASSED' if test3 else '❌ FAILED'}")
    
    if test1 and test2 and test3:
        print("\n🎉 All basic tests passed!")
        print("\nThe thread management fixes have been applied:")
        print("  • Proper thread cleanup with quit() and wait()")
        print("  • deleteLater() calls for proper Qt object cleanup")
        print("  • finally blocks in worker run() methods")
        print("  • Improved error handling in completion methods")
        
        print("\nYou can now try running the application again:")
        print("  python run.py")
        
        print("\nThe 'QThread: Destroyed while thread is still running' error should be resolved.")
        return 0
    else:
        print("\n❌ Some basic tests failed.")
        print("Please check the error messages above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
