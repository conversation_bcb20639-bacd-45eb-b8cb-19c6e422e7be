# Chinese Pharmacopoeia PDF Splitter - User Manual

## Overview

The Chinese Pharmacopoeia PDF Splitter is a specialized tool designed to analyze and split Chinese Pharmacopoeia 2020 Edition PDF files into individual chapters. This manual provides detailed instructions for using the application effectively.

## System Requirements

- **Operating System**: Windows 11
- **Python**: 3.8 or higher
- **Memory**: 4GB RAM minimum (8GB recommended for large PDFs)
- **Storage**: 1GB free space for temporary files and output

## Installation

1. **Download the application** to your desired directory
2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
3. **Verify installation** by running the test suite:
   ```bash
   python test_app.py
   ```

## Getting Started

### Launching the Application

1. Open Command Prompt or PowerShell
2. Navigate to the application directory
3. Run the application:
   ```bash
   python run.py
   ```

The main window will appear with the following sections:
- **Input Settings**: File selection and volume configuration
- **Output Options**: Filename customization settings
- **Control Buttons**: Analysis and splitting controls
- **Progress**: Real-time progress tracking
- **Log**: Detailed operation messages

## Step-by-Step Usage Guide

### Step 1: Select Input PDF File

1. Click the **"Browse..."** button next to "PDF File"
2. Navigate to your Chinese Pharmacopoeia PDF file
3. Select the file and click "Open"
4. The file path will appear in the text field

**Supported Files:**
- Chinese Pharmacopoeia 2020 Edition (all volumes)
- PDF format only
- File size: Up to 500MB (larger files may work but require more memory)

### Step 2: Choose Volume

1. Use the **"Volume"** dropdown menu
2. Select the appropriate volume:
   - **Volume 1**: 药品 (Drugs)
   - **Volume 2**: 生物制品 (Biological Products)  
   - **Volume 3**: 药用辅料 (Pharmaceutical Excipients)
   - **Volume 4**: 通则和检验方法 (General Rules and Test Methods)

**Important**: Selecting the correct volume ensures optimal chapter detection accuracy.

### Step 3: Configure Output Directory

1. Specify the output directory in the **"Output Directory"** field
2. Click **"Browse..."** to select a different directory if needed
3. Default directory is `output/` in the application folder

**Note**: The application will create the output directory if it doesn't exist.

### Step 4: Set Output Options

Configure filename generation options:

#### Use Chinese Characters in Filenames
- **Checked**: Filenames will contain Chinese characters
  - Example: `第一章 总则_1-20_202509241142.pdf`
- **Unchecked**: Filenames will use English translations
  - Example: `Chapter General Rules_1-20_202509241142.pdf`

#### Include Page Numbers in Filenames
- **Checked**: Filenames will include page ranges
  - Example: `第一章 总则_1-20_202509241142.pdf`
- **Unchecked**: Filenames will not include page numbers
  - Example: `第一章 总则_202509241142.pdf`

#### Include Timestamp in Filenames
- **Checked**: Filenames will include timestamp to avoid conflicts
  - Example: `第一章 总则_1-20_202509241142.pdf`
- **Unchecked**: Filenames will not include timestamp
  - Example: `第一章 总则_1-20.pdf`

### Step 5: Analyze PDF

1. Click the **"Analyze PDF"** button
2. The application will:
   - Scan the PDF for chapter titles
   - Analyze text formatting and structure
   - Identify chapter boundaries
   - Display progress in the log area

**Analysis Process:**
- **Duration**: 1-5 minutes depending on PDF size
- **Progress**: Indeterminate progress bar during analysis
- **Output**: List of detected chapters in the log

**What to Expect:**
- The log will show detected chapters with page ranges
- Analysis results are displayed upon completion
- The "Split PDF" button becomes enabled after successful analysis

### Step 6: Split PDF

1. Click the **"Split PDF"** button after analysis
2. Monitor progress using the progress bar and log messages
3. Use control buttons as needed:
   - **Pause**: Temporarily stop the splitting process
   - **Resume**: Continue after pausing
   - **Cancel**: Stop and abort the operation

**Splitting Process:**
- **Duration**: Varies based on PDF size and number of chapters
- **Progress**: Shows current chapter being processed
- **Output**: Individual PDF files for each chapter

## Understanding the Interface

### Progress Indicators

1. **Progress Bar**: Shows completion percentage during splitting
2. **Progress Label**: Displays current operation status
3. **Current Chapter Label**: Shows which chapter is being processed
4. **Log Area**: Detailed messages about operations

### Control Buttons

- **Analyze PDF**: Start PDF analysis to detect chapters
- **Split PDF**: Begin splitting process (enabled after analysis)
- **Pause**: Pause current operation
- **Resume**: Resume paused operation  
- **Cancel**: Cancel current operation

### Status Messages

The application provides various status messages:
- **Ready**: Application is ready for input
- **Analyzing...**: PDF analysis in progress
- **Analysis completed**: Chapter detection finished
- **Splitting...**: PDF splitting in progress
- **Completed**: Operation finished successfully
- **Error**: Operation failed (check log for details)

## Output Files

### File Organization

Split PDF files are saved to the specified output directory with the following structure:
```
output/
├── 第一章 总则_1-20_202509241142.pdf
├── 第二章 药品质量标准_21-150_202509241142.pdf
├── 第三章 检验方法_151-300_202509241142.pdf
└── ...
```

### File Naming Convention

The filename format depends on your selected options:
```
[Chapter_Title]_[Page_Range]_[Timestamp].pdf
```

**Components:**
- **Chapter_Title**: Detected chapter title (Chinese or English)
- **Page_Range**: Start and end page numbers (optional)
- **Timestamp**: Creation timestamp in YYYYMMDDHHMM format (optional)

## Troubleshooting

### Common Issues and Solutions

#### 1. Application Won't Start
**Symptoms**: Error messages when running `python run.py`
**Solutions**:
- Verify Python 3.8+ is installed
- Install missing dependencies: `pip install -r requirements.txt`
- Check that PyQt6 is properly installed: `pip install PyQt6`

#### 2. PDF Analysis Fails
**Symptoms**: "Analysis failed" message in log
**Solutions**:
- Verify the PDF file is not corrupted
- Ensure the file is a Chinese Pharmacopoeia document
- Check that the file is not password-protected
- Try with a smaller PDF file first

#### 3. No Chapters Detected
**Symptoms**: Analysis completes but finds 0 chapters
**Solutions**:
- Verify you selected the correct volume
- Check that the PDF contains text (not just images)
- Try adjusting confidence threshold in configuration file

#### 4. Splitting Process Stops
**Symptoms**: Splitting stops unexpectedly
**Solutions**:
- Check available disk space
- Ensure output directory is writable
- Review log messages for specific errors
- Try splitting a smaller section first

#### 5. Memory Issues
**Symptoms**: Application becomes slow or crashes with large PDFs
**Solutions**:
- Close other applications to free memory
- Process smaller PDF files
- Increase virtual memory settings
- Use a computer with more RAM

### Error Messages

#### "PDF file not found"
- The selected PDF file path is invalid
- Re-select the PDF file using the Browse button

#### "Failed to create output directory"
- Insufficient permissions to create directories
- Choose a different output location
- Run application as administrator if necessary

#### "Analysis timeout"
- PDF file is too large or complex
- Try with a smaller file
- Increase timeout in configuration file

### Getting Help

#### Log Files
- Check the `logs/` directory for detailed error information
- Log files are named with timestamps: `log_YYYYMMDD_HHMMSS.log`
- Share relevant log entries when reporting issues

#### Configuration
- Configuration file: `config/config.yaml`
- Adjust settings for better performance or compatibility
- Backup configuration before making changes

## Advanced Features

### Configuration Customization

Edit `config/config.yaml` to customize:
- Chapter detection parameters
- Font size thresholds
- Confidence scores
- Output naming patterns
- Logging levels

### Batch Processing

For processing multiple PDFs:
1. Process each PDF individually through the GUI
2. Use different output directories for each volume
3. Monitor log files for any issues

### Performance Optimization

For better performance with large PDFs:
- Close unnecessary applications
- Use SSD storage for temporary files
- Increase system virtual memory
- Process during off-peak hours

## Best Practices

1. **File Management**:
   - Keep original PDFs as backups
   - Use descriptive output directory names
   - Regularly clean up temporary files

2. **Quality Control**:
   - Review analysis results before splitting
   - Spot-check output files for completeness
   - Verify page ranges are correct

3. **Workflow**:
   - Test with small PDFs first
   - Process one volume at a time
   - Keep detailed records of processing

## Support and Maintenance

### Regular Maintenance
- Update dependencies periodically
- Clear old log files
- Backup configuration settings
- Monitor disk space usage

### Version Updates
- Check for application updates
- Review changelog for new features
- Test with sample files after updates

This manual covers the essential aspects of using the Chinese Pharmacopoeia PDF Splitter. For technical issues or feature requests, refer to the application logs and configuration files for detailed information.
