#!/usr/bin/env python3
"""
Demo script for Chinese Pharmacopoeia PDF Analysis
Tests the PDF analysis functionality with actual PDF files
"""

import sys
import os
from pathlib import Path
import json

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from core.pdf_analyzer import PDFAnalyzer
from models.chapter_info import VolumeType
from utils.logger import get_logger


def find_pdf_files():
    """Find available PDF files for testing"""
    pdf_files = []
    current_dir = Path(".")
    
    for pdf_file in current_dir.glob("*.pdf"):
        if pdf_file.is_file():
            pdf_files.append(pdf_file)
    
    return pdf_files


def analyze_pdf_sample(pdf_path, volume, max_pages=10):
    """
    Analyze a sample of pages from PDF to test functionality
    
    Args:
        pdf_path: Path to PDF file
        volume: Volume type
        max_pages: Maximum pages to analyze (for demo purposes)
    """
    print(f"\n=== Analyzing PDF: {pdf_path} ===")
    print(f"Volume: {volume.value}")
    print(f"Max pages to analyze: {max_pages}")
    
    try:
        analyzer = PDFAnalyzer()
        logger = get_logger()
        
        # For demo, we'll just test the basic functionality
        # In a real scenario, you'd analyze the full PDF
        
        print("\n1. Testing chapter pattern matching...")
        
        # Test some sample chapter titles
        sample_titles = [
            "第一章 总则",
            "第二章 药品质量标准",
            "第三章 检验方法和限度标准",
            "附录A 高效液相色谱法",
            "附录B 气相色谱法",
            "索引"
        ]
        
        detected_chapters = []
        for i, title in enumerate(sample_titles, 1):
            chapter_info = analyzer._detect_chapter_title(title, 14.0, "Arial-Bold", i)
            if chapter_info:
                detected_chapters.append(chapter_info)
                print(f"  ✓ Detected: {title} (confidence: {chapter_info.confidence_score:.2f})")
            else:
                print(f"  ✗ Not detected: {title}")
        
        print(f"\n2. Chapter detection summary:")
        print(f"  - Total test titles: {len(sample_titles)}")
        print(f"  - Detected chapters: {len(detected_chapters)}")
        
        # Test post-processing
        if detected_chapters:
            print(f"\n3. Testing post-processing...")
            
            # Create a mock analysis result
            from models.chapter_info import PDFAnalysisResult
            result = PDFAnalysisResult(
                pdf_path=str(pdf_path),
                volume=volume,
                total_pages=200  # Mock total pages
            )
            
            # Add chapters with mock page ranges
            for i, chapter in enumerate(detected_chapters):
                chapter.start_page = i * 30 + 1
                result.add_chapter(chapter)
            
            # Post-process
            processed_result = analyzer._post_process_chapters(result)
            
            print(f"  - Chapters after post-processing: {len(processed_result.chapters)}")
            for chapter in processed_result.chapters:
                print(f"    • {chapter.title}: pages {chapter.page_range_str}")
        
        print(f"\n4. Testing filename generation...")
        if detected_chapters:
            chapter = detected_chapters[0]
            
            # Test different filename options
            filename_options = [
                (True, True, True, "202509241142"),
                (False, True, True, "202509241142"),
                (True, False, False, ""),
            ]
            
            for use_chinese, include_pages, include_timestamp, timestamp in filename_options:
                filename = chapter.generate_filename(
                    use_chinese=use_chinese,
                    include_pages=include_pages,
                    include_timestamp=include_timestamp,
                    timestamp=timestamp
                )
                print(f"    • Chinese={use_chinese}, Pages={include_pages}, Time={include_timestamp}: {filename}")
        
        print(f"\n✅ Demo analysis completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ Demo analysis failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main demo function"""
    print("Chinese Pharmacopoeia PDF Analyzer - Demo")
    print("=" * 50)
    
    # Find available PDF files
    pdf_files = find_pdf_files()
    
    if not pdf_files:
        print("❌ No PDF files found in current directory.")
        print("Please place some Chinese Pharmacopoeia PDF files in the current directory.")
        return 1
    
    print(f"Found {len(pdf_files)} PDF files:")
    for i, pdf_file in enumerate(pdf_files, 1):
        file_size = pdf_file.stat().st_size / (1024 * 1024)  # MB
        print(f"  {i}. {pdf_file.name} ({file_size:.1f} MB)")
    
    # For demo, we'll test with the first PDF file
    test_pdf = pdf_files[0]
    
    # Determine volume based on filename
    filename_lower = test_pdf.name.lower()
    if "1部" in filename_lower or "一部" in filename_lower:
        volume = VolumeType.VOLUME_1
    elif "2部" in filename_lower or "二部" in filename_lower:
        volume = VolumeType.VOLUME_2
    elif "3部" in filename_lower or "三部" in filename_lower:
        volume = VolumeType.VOLUME_3
    elif "4部" in filename_lower or "四部" in filename_lower:
        volume = VolumeType.VOLUME_4
    else:
        volume = VolumeType.VOLUME_1  # Default
    
    # Run demo analysis
    success = analyze_pdf_sample(test_pdf, volume, max_pages=10)
    
    if success:
        print(f"\n🎉 Demo completed successfully!")
        print(f"\nTo run the full application with GUI:")
        print(f"  python run.py")
        print(f"\nTo run tests:")
        print(f"  python test_app.py")
        return 0
    else:
        print(f"\n❌ Demo failed. Please check the error messages above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
