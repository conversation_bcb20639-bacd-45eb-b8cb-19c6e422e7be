"""
Unit tests for PDF Splitter module
"""

import unittest
import tempfile
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add src to path for imports
import sys
src_path = Path(__file__).parent.parent / "src"
sys.path.insert(0, str(src_path))

from core.pdf_splitter import PDFSplitter, SplittingProgress
from models.chapter_info import ChapterInfo, PDFAnalysisResult, VolumeType, ChapterType


class TestSplittingProgress(unittest.TestCase):
    """Test cases for SplittingProgress class"""
    
    def test_init(self):
        """Test SplittingProgress initialization"""
        progress = SplittingProgress(10)
        
        self.assertEqual(progress.total_chapters, 10)
        self.assertEqual(progress.current_chapter, 0)
        self.assertEqual(progress.current_chapter_name, "")
        self.assertFalse(progress.is_paused)
        self.assertFalse(progress.is_cancelled)
        self.assertEqual(len(progress.errors), 0)
    
    def test_update(self):
        """Test progress update"""
        progress = SplittingProgress(10)
        progress.update(5, "第五章 测试")
        
        self.assertEqual(progress.current_chapter, 5)
        self.assertEqual(progress.current_chapter_name, "第五章 测试")
    
    def test_add_error(self):
        """Test adding errors"""
        progress = SplittingProgress(10)
        progress.add_error("Test error")
        
        self.assertEqual(len(progress.errors), 1)
        self.assertEqual(progress.errors[0], "Test error")
    
    def test_progress_percentage(self):
        """Test progress percentage calculation"""
        progress = SplittingProgress(10)
        
        # Test 0%
        self.assertEqual(progress.progress_percentage, 0.0)
        
        # Test 50%
        progress.update(5, "Test")
        self.assertEqual(progress.progress_percentage, 50.0)
        
        # Test 100%
        progress.update(10, "Test")
        self.assertEqual(progress.progress_percentage, 100.0)
        
        # Test edge case: 0 total chapters
        progress_zero = SplittingProgress(0)
        self.assertEqual(progress_zero.progress_percentage, 0.0)


class TestPDFSplitter(unittest.TestCase):
    """Test cases for PDFSplitter class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.splitter = PDFSplitter()
        
        # Create test analysis result
        self.test_result = PDFAnalysisResult(
            pdf_path="test.pdf",
            volume=VolumeType.VOLUME_1,
            total_pages=100
        )
        
        # Add test chapters
        chapters = [
            ChapterInfo("第一章 总则", 1, 10),
            ChapterInfo("第二章 药品", 11, 30),
            ChapterInfo("附录A 测试", 31, 40),
        ]
        
        for chapter in chapters:
            self.test_result.add_chapter(chapter)
    
    def test_init(self):
        """Test PDFSplitter initialization"""
        self.assertIsNotNone(self.splitter.config_manager)
        self.assertIsNotNone(self.splitter.pdf_config)
        self.assertIsNotNone(self.splitter.dirs_config)
        self.assertIsNotNone(self.splitter.error_config)
        self.assertIsNotNone(self.splitter.logger)
        self.assertIsNone(self.splitter.progress)
        self.assertIsNone(self.splitter.progress_callback)
    
    def test_pause_resume_cancel(self):
        """Test pause, resume, and cancel operations"""
        # Initialize progress
        self.splitter.progress = SplittingProgress(5)
        
        # Test pause
        self.splitter.pause_splitting()
        self.assertTrue(self.splitter.progress.is_paused)
        
        # Test resume
        self.splitter.resume_splitting()
        self.assertFalse(self.splitter.progress.is_paused)
        
        # Test cancel
        self.splitter.cancel_splitting()
        self.assertTrue(self.splitter.progress.is_cancelled)
    
    def test_get_progress(self):
        """Test getting progress"""
        # Test when no progress exists
        self.assertIsNone(self.splitter.get_progress())
        
        # Test when progress exists
        progress = SplittingProgress(5)
        self.splitter.progress = progress
        self.assertEqual(self.splitter.get_progress(), progress)
    
    @patch('PyPDF2.PdfReader')
    @patch('PyPDF2.PdfWriter')
    @patch('builtins.open')
    @patch('pathlib.Path.mkdir')
    @patch('pathlib.Path.exists')
    @patch('pathlib.Path.stat')
    def test_split_chapter_success(self, mock_stat, mock_exists, mock_mkdir, 
                                  mock_open, mock_pdf_writer, mock_pdf_reader):
        """Test successful chapter splitting"""
        # Setup mocks
        mock_exists.return_value = True
        mock_stat.return_value.st_size = 1000  # Non-zero file size
        
        mock_reader = Mock()
        mock_reader.pages = [Mock() for _ in range(10)]  # 10 pages
        
        mock_writer = Mock()
        mock_pdf_writer.return_value = mock_writer
        
        # Test chapter
        chapter = ChapterInfo("第一章 总则", 1, 5)
        
        # Test splitting
        result = self.splitter._split_chapter(mock_reader, chapter, "output.pdf")
        
        self.assertTrue(result)
        mock_writer.add_page.assert_called()
        mock_writer.write.assert_called()
    
    @patch('PyPDF2.PdfReader')
    def test_split_chapter_failure(self, mock_pdf_reader):
        """Test chapter splitting failure"""
        # Setup mock to raise exception
        mock_reader = Mock()
        mock_reader.pages = []
        
        chapter = ChapterInfo("第一章 总则", 1, 5)
        
        # Test splitting with exception
        with patch('PyPDF2.PdfWriter') as mock_writer:
            mock_writer.side_effect = Exception("Test error")
            result = self.splitter._split_chapter(mock_reader, chapter, "output.pdf")
            self.assertFalse(result)
    
    def test_wait_if_paused(self):
        """Test waiting when paused"""
        # Test when no progress
        self.splitter._wait_if_paused()  # Should not hang
        
        # Test when not paused
        self.splitter.progress = SplittingProgress(5)
        self.splitter._wait_if_paused()  # Should not hang
        
        # Test when cancelled (should not wait)
        self.splitter.progress.is_cancelled = True
        self.splitter.progress.is_paused = True
        self.splitter._wait_if_paused()  # Should not hang
    
    @patch('tempfile.mkdtemp')
    @patch('pathlib.Path.mkdir')
    def test_split_pdf_output_directory_creation(self, mock_mkdir, mock_mkdtemp):
        """Test output directory creation"""
        mock_mkdir.return_value = None
        
        with patch.object(self.splitter, '_split_chapter', return_value=True):
            with patch('builtins.open', mock_open()):
                with patch('PyPDF2.PdfReader'):
                    result = self.splitter.split_pdf(
                        self.test_result,
                        "test_output",
                        use_chinese_names=True,
                        include_page_numbers=True,
                        include_timestamp=True
                    )
        
        mock_mkdir.assert_called()
    
    def test_filename_generation_options(self):
        """Test different filename generation options"""
        chapter = ChapterInfo("第一章 总则", 1, 10)
        
        # Test Chinese names with pages and timestamp
        filename1 = chapter.generate_filename(
            use_chinese=True,
            include_pages=True,
            include_timestamp=True,
            timestamp="202509241142"
        )
        self.assertIn("第一章 总则", filename1)
        self.assertIn("1-10", filename1)
        self.assertIn("202509241142", filename1)
        
        # Test English names without pages and timestamp
        filename2 = chapter.generate_filename(
            use_chinese=False,
            include_pages=False,
            include_timestamp=False,
            timestamp=""
        )
        self.assertNotIn("第一章 总则", filename2)
        self.assertNotIn("1-10", filename2)
        self.assertNotIn("202509241142", filename2)


class TestPDFSplitterIntegration(unittest.TestCase):
    """Integration tests for PDF splitter"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.splitter = PDFSplitter()
        
        # Create more comprehensive test data
        self.test_result = PDFAnalysisResult(
            pdf_path="test_comprehensive.pdf",
            volume=VolumeType.VOLUME_1,
            total_pages=200
        )
        
        # Add various types of chapters
        chapters = [
            ChapterInfo("第一章 总则", 1, 20, chapter_type=ChapterType.MAIN_CHAPTER),
            ChapterInfo("第二章 药品质量标准", 21, 80, chapter_type=ChapterType.MAIN_CHAPTER),
            ChapterInfo("第三章 检验方法", 81, 150, chapter_type=ChapterType.MAIN_CHAPTER),
            ChapterInfo("附录A 高效液相色谱法", 151, 180, chapter_type=ChapterType.APPENDIX),
            ChapterInfo("附录B 气相色谱法", 181, 190, chapter_type=ChapterType.APPENDIX),
            ChapterInfo("索引", 191, 200, chapter_type=ChapterType.INDEX),
        ]
        
        for chapter in chapters:
            self.test_result.add_chapter(chapter)
    
    def test_comprehensive_splitting_workflow(self):
        """Test complete splitting workflow"""
        # Test progress callback
        progress_updates = []
        
        def progress_callback(progress):
            progress_updates.append({
                'current': progress.current_chapter,
                'total': progress.total_chapters,
                'name': progress.current_chapter_name,
                'percentage': progress.progress_percentage
            })
        
        # Mock the actual PDF operations
        with patch.object(self.splitter, '_split_chapter', return_value=True):
            with patch('builtins.open', mock_open()):
                with patch('PyPDF2.PdfReader'):
                    with patch('pathlib.Path.mkdir'):
                        result = self.splitter.split_pdf(
                            self.test_result,
                            "test_output",
                            use_chinese_names=True,
                            include_page_numbers=True,
                            include_timestamp=True,
                            progress_callback=progress_callback
                        )
        
        # Verify results
        self.assertEqual(result['total_chapters'], 6)
        self.assertEqual(result['successful_splits'], 6)
        self.assertEqual(result['failed_splits'], 0)
        self.assertEqual(len(result['output_files']), 6)
        self.assertEqual(len(result['errors']), 0)
        self.assertFalse(result['cancelled'])
        
        # Verify progress updates
        self.assertEqual(len(progress_updates), 6)
        self.assertEqual(progress_updates[-1]['current'], 6)
        self.assertEqual(progress_updates[-1]['percentage'], 100.0)
    
    def test_error_handling_continue_on_error(self):
        """Test error handling with continue_on_error=True"""
        # Mock split_chapter to fail for second chapter
        def mock_split_chapter(reader, chapter, output_path):
            if "第二章" in chapter.title:
                return False
            return True
        
        with patch.object(self.splitter, '_split_chapter', side_effect=mock_split_chapter):
            with patch('builtins.open', mock_open()):
                with patch('PyPDF2.PdfReader'):
                    with patch('pathlib.Path.mkdir'):
                        result = self.splitter.split_pdf(
                            self.test_result,
                            "test_output"
                        )
        
        # Should continue processing despite one failure
        self.assertEqual(result['successful_splits'], 5)
        self.assertEqual(result['failed_splits'], 1)
        self.assertGreater(len(result['errors']), 0)


def mock_open(*args, **kwargs):
    """Mock open function for testing"""
    return MagicMock()


if __name__ == '__main__':
    unittest.main()
