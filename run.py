#!/usr/bin/env python3
"""
Main entry point for Chinese Pharmacopoeia PDF Splitter
This script initializes the application and starts the GUI
"""

import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

def main():
    """Main entry point for the application"""
    try:
        # Import main application after path setup
        from main import run_application
        
        # Run the application
        return run_application()
        
    except ImportError as e:
        print(f"Error importing application modules: {e}")
        print("Please ensure all dependencies are installed: pip install -r requirements.txt")
        return 1
    except Exception as e:
        print(f"Unexpected error starting application: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
