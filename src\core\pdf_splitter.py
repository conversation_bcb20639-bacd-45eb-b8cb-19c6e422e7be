"""
PDF Splitter Module
Splits Chinese Pharmacopoeia PDF files into individual chapters based on analysis results
"""

import os
import shutil
from pathlib import Path
from datetime import datetime
from typing import List, Optional, Callable, Dict, Any
import PyPDF2
import fitz  # PyMuPDF

try:
    from ..models.chapter_info import ChapterInfo, PDFAnalysisResult, VolumeType
    from ..utils.config_manager import get_config_manager
    from ..utils.logger import get_logger
except ImportError:
    # Fallback for direct execution
    from models.chapter_info import ChapterInfo, PDFAnalysisResult, VolumeType
    from utils.config_manager import get_config_manager
    from utils.logger import get_logger


class SplittingProgress:
    """Class to track splitting progress"""
    
    def __init__(self, total_chapters: int):
        self.total_chapters = total_chapters
        self.current_chapter = 0
        self.current_chapter_name = ""
        self.is_paused = False
        self.is_cancelled = False
        self.errors: List[str] = []
    
    def update(self, chapter_index: int, chapter_name: str) -> None:
        """Update progress information"""
        self.current_chapter = chapter_index
        self.current_chapter_name = chapter_name
    
    def add_error(self, error: str) -> None:
        """Add error to the error list"""
        self.errors.append(error)
    
    @property
    def progress_percentage(self) -> float:
        """Get progress as percentage"""
        if self.total_chapters == 0:
            return 0.0
        return (self.current_chapter / self.total_chapters) * 100


class PDFSplitter:
    """
    PDF splitter class for splitting Chinese Pharmacopoeia PDFs by chapters
    Supports pause/resume/cancel operations and progress tracking
    """
    
    def __init__(self):
        """Initialize PDF splitter with configuration"""
        self.config_manager = get_config_manager()
        self.pdf_config = self.config_manager.get_pdf_config()
        self.dirs_config = self.config_manager.get_directories_config()
        self.error_config = self.config_manager.get_error_handling_config()
        self.logger = get_logger()
        
        # Progress tracking
        self.progress: Optional[SplittingProgress] = None
        self.progress_callback: Optional[Callable[[SplittingProgress], None]] = None
    
    def split_pdf(self, analysis_result: PDFAnalysisResult, output_dir: str,
                  use_chinese_names: bool = True, include_page_numbers: bool = True,
                  include_timestamp: bool = True,
                  progress_callback: Optional[Callable[[SplittingProgress], None]] = None) -> Dict[str, Any]:
        """
        Split PDF into individual chapter files based on analysis result
        
        Args:
            analysis_result: PDF analysis result containing chapter information
            output_dir: Directory to save split PDF files
            use_chinese_names: Whether to use Chinese characters in filenames
            include_page_numbers: Whether to include page numbers in filenames
            include_timestamp: Whether to include timestamp in filenames
            progress_callback: Optional callback function for progress updates
            
        Returns:
            Dict[str, Any]: Splitting result summary
        """
        self.logger.info(f"Starting PDF splitting: {analysis_result.pdf_path}")
        
        # Initialize progress tracking
        self.progress = SplittingProgress(len(analysis_result.chapters))
        self.progress_callback = progress_callback
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Generate timestamp for filenames
        timestamp = datetime.now().strftime(self.pdf_config.timestamp_format)
        
        # Initialize result summary
        result_summary = {
            'total_chapters': len(analysis_result.chapters),
            'successful_splits': 0,
            'failed_splits': 0,
            'output_files': [],
            'errors': [],
            'cancelled': False
        }
        
        try:
            # Open source PDF
            with open(analysis_result.pdf_path, 'rb') as source_file:
                source_pdf = PyPDF2.PdfReader(source_file)
                
                # Process each chapter
                for i, chapter in enumerate(analysis_result.chapters):
                    # Check for cancellation
                    if self.progress and self.progress.is_cancelled:
                        result_summary['cancelled'] = True
                        self.logger.info("PDF splitting cancelled by user")
                        break
                    
                    # Wait if paused
                    self._wait_if_paused()
                    
                    # Update progress
                    self.progress.update(i + 1, chapter.title)
                    if self.progress_callback:
                        self.progress_callback(self.progress)
                    
                    # Split chapter
                    try:
                        output_filename = chapter.generate_filename(
                            use_chinese=use_chinese_names,
                            include_pages=include_page_numbers,
                            include_timestamp=include_timestamp,
                            timestamp=timestamp
                        )
                        
                        output_file_path = output_path / output_filename
                        
                        success = self._split_chapter(
                            source_pdf, chapter, str(output_file_path)
                        )
                        
                        if success:
                            result_summary['successful_splits'] += 1
                            result_summary['output_files'].append(str(output_file_path))
                            chapter.processed = True
                            chapter.output_filename = output_filename
                            
                            self.logger.info(f"Successfully split chapter: {chapter.title}")
                        else:
                            result_summary['failed_splits'] += 1
                            error_msg = f"Failed to split chapter: {chapter.title}"
                            result_summary['errors'].append(error_msg)
                            chapter.error_message = error_msg
                            
                    except Exception as e:
                        error_msg = f"Error splitting chapter '{chapter.title}': {str(e)}"
                        self.logger.error(error_msg)
                        result_summary['failed_splits'] += 1
                        result_summary['errors'].append(error_msg)
                        chapter.error_message = error_msg
                        
                        if not self.error_config.continue_on_error:
                            break
        
        except Exception as e:
            error_msg = f"Critical error during PDF splitting: {str(e)}"
            self.logger.error(error_msg)
            result_summary['errors'].append(error_msg)
        
        # Log final results
        self.logger.info(f"PDF splitting completed. Success: {result_summary['successful_splits']}, "
                        f"Failed: {result_summary['failed_splits']}")
        
        return result_summary
    
    def _split_chapter(self, source_pdf: PyPDF2.PdfReader, chapter: ChapterInfo, 
                      output_path: str) -> bool:
        """
        Split a single chapter from the source PDF
        
        Args:
            source_pdf: Source PDF reader object
            chapter: Chapter information
            output_path: Output file path
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Create new PDF writer
            pdf_writer = PyPDF2.PdfWriter()
            
            # Add pages for this chapter
            start_page = chapter.start_page - 1  # Convert to 0-based index
            end_page = (chapter.end_page or chapter.start_page) - 1
            
            for page_num in range(start_page, end_page + 1):
                if page_num < len(source_pdf.pages):
                    page = source_pdf.pages[page_num]
                    pdf_writer.add_page(page)
                else:
                    self.logger.warning(f"Page {page_num + 1} not found in source PDF")
            
            # Write to output file
            with open(output_path, 'wb') as output_file:
                pdf_writer.write(output_file)
            
            # Verify the output file
            if Path(output_path).exists() and Path(output_path).stat().st_size > 0:
                self.logger.info(f"Chapter split successfully: {output_path}")
                return True
            else:
                self.logger.error(f"Output file is empty or not created: {output_path}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error splitting chapter: {str(e)}")
            return False
    
    def _wait_if_paused(self) -> None:
        """Wait while splitting is paused"""
        if self.progress:
            while self.progress.is_paused and not self.progress.is_cancelled:
                # Sleep briefly to avoid busy waiting
                import time
                time.sleep(0.1)
    
    def pause_splitting(self) -> None:
        """Pause the splitting process"""
        if self.progress:
            self.progress.is_paused = True
            self.logger.info("PDF splitting paused")
    
    def resume_splitting(self) -> None:
        """Resume the splitting process"""
        if self.progress:
            self.progress.is_paused = False
            self.logger.info("PDF splitting resumed")
    
    def cancel_splitting(self) -> None:
        """Cancel the splitting process"""
        if self.progress:
            self.progress.is_cancelled = True
            self.logger.info("PDF splitting cancelled")
    
    def get_progress(self) -> Optional[SplittingProgress]:
        """Get current splitting progress"""
        return self.progress
    
    def split_with_pymupdf(self, analysis_result: PDFAnalysisResult, output_dir: str,
                          use_chinese_names: bool = True, include_page_numbers: bool = True,
                          include_timestamp: bool = True) -> Dict[str, Any]:
        """
        Alternative splitting method using PyMuPDF (for better performance with large PDFs)
        
        Args:
            analysis_result: PDF analysis result
            output_dir: Output directory
            use_chinese_names: Use Chinese names in filenames
            include_page_numbers: Include page numbers in filenames
            include_timestamp: Include timestamp in filenames
            
        Returns:
            Dict[str, Any]: Splitting result summary
        """
        self.logger.info(f"Starting PDF splitting with PyMuPDF: {analysis_result.pdf_path}")
        
        # Initialize progress tracking
        self.progress = SplittingProgress(len(analysis_result.chapters))
        
        # Create output directory
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)
        
        # Generate timestamp
        timestamp = datetime.now().strftime(self.pdf_config.timestamp_format)
        
        # Initialize result summary
        result_summary = {
            'total_chapters': len(analysis_result.chapters),
            'successful_splits': 0,
            'failed_splits': 0,
            'output_files': [],
            'errors': []
        }
        
        try:
            # Open source PDF with PyMuPDF
            doc = fitz.open(analysis_result.pdf_path)
            
            for i, chapter in enumerate(analysis_result.chapters):
                # Check for cancellation
                if self.progress and self.progress.is_cancelled:
                    break
                
                # Wait if paused
                self._wait_if_paused()
                
                # Update progress
                self.progress.update(i + 1, chapter.title)
                if self.progress_callback:
                    self.progress_callback(self.progress)
                
                try:
                    # Generate output filename
                    output_filename = chapter.generate_filename(
                        use_chinese=use_chinese_names,
                        include_pages=include_page_numbers,
                        include_timestamp=include_timestamp,
                        timestamp=timestamp
                    )
                    
                    output_file_path = output_path / output_filename
                    
                    # Create new document for this chapter
                    chapter_doc = fitz.open()
                    
                    # Add pages to chapter document
                    start_page = chapter.start_page - 1  # Convert to 0-based
                    end_page = (chapter.end_page or chapter.start_page) - 1
                    
                    for page_num in range(start_page, end_page + 1):
                        if page_num < len(doc):
                            chapter_doc.insert_pdf(doc, from_page=page_num, to_page=page_num)
                    
                    # Save chapter document
                    chapter_doc.save(str(output_file_path))
                    chapter_doc.close()
                    
                    # Verify output
                    if output_file_path.exists() and output_file_path.stat().st_size > 0:
                        result_summary['successful_splits'] += 1
                        result_summary['output_files'].append(str(output_file_path))
                        chapter.processed = True
                        chapter.output_filename = output_filename
                        
                        self.logger.info(f"Successfully split chapter with PyMuPDF: {chapter.title}")
                    else:
                        raise Exception("Output file is empty or not created")
                
                except Exception as e:
                    error_msg = f"Error splitting chapter '{chapter.title}': {str(e)}"
                    self.logger.error(error_msg)
                    result_summary['failed_splits'] += 1
                    result_summary['errors'].append(error_msg)
                    chapter.error_message = error_msg
            
            doc.close()
            
        except Exception as e:
            error_msg = f"Critical error during PyMuPDF splitting: {str(e)}"
            self.logger.error(error_msg)
            result_summary['errors'].append(error_msg)
        
        self.logger.info(f"PyMuPDF splitting completed. Success: {result_summary['successful_splits']}, "
                        f"Failed: {result_summary['failed_splits']}")
        
        return result_summary
