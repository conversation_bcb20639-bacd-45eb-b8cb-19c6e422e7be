"""
File Utilities Module
Provides utility functions for file operations and path management
"""

import os
import shutil
from pathlib import Path
from typing import List, Optional, Dict, Any
import json
from datetime import datetime

try:
    from .logger import get_logger
except ImportError:
    from logger import get_logger


class FileManager:
    """
    File manager class for handling file operations
    Provides safe file operations with error handling and logging
    """
    
    def __init__(self):
        """Initialize file manager"""
        self.logger = get_logger()
    
    def ensure_directory(self, directory_path: str) -> bool:
        """
        Ensure directory exists, create if it doesn't
        
        Args:
            directory_path: Path to directory
            
        Returns:
            bool: True if directory exists or was created successfully
        """
        try:
            Path(directory_path).mkdir(parents=True, exist_ok=True)
            self.logger.debug(f"Directory ensured: {directory_path}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to create directory {directory_path}: {e}")
            return False
    
    def safe_copy_file(self, source_path: str, destination_path: str, 
                      overwrite: bool = False) -> bool:
        """
        Safely copy file from source to destination
        
        Args:
            source_path: Source file path
            destination_path: Destination file path
            overwrite: Whether to overwrite existing file
            
        Returns:
            bool: True if copy was successful
        """
        try:
            source = Path(source_path)
            destination = Path(destination_path)
            
            if not source.exists():
                self.logger.error(f"Source file does not exist: {source_path}")
                return False
            
            if destination.exists() and not overwrite:
                self.logger.warning(f"Destination file exists and overwrite=False: {destination_path}")
                return False
            
            # Ensure destination directory exists
            self.ensure_directory(str(destination.parent))
            
            # Copy file
            shutil.copy2(source, destination)
            
            self.logger.info(f"File copied successfully: {source_path} -> {destination_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to copy file: {e}")
            return False
    
    def safe_move_file(self, source_path: str, destination_path: str,
                      overwrite: bool = False) -> bool:
        """
        Safely move file from source to destination
        
        Args:
            source_path: Source file path
            destination_path: Destination file path
            overwrite: Whether to overwrite existing file
            
        Returns:
            bool: True if move was successful
        """
        try:
            source = Path(source_path)
            destination = Path(destination_path)
            
            if not source.exists():
                self.logger.error(f"Source file does not exist: {source_path}")
                return False
            
            if destination.exists() and not overwrite:
                self.logger.warning(f"Destination file exists and overwrite=False: {destination_path}")
                return False
            
            # Ensure destination directory exists
            self.ensure_directory(str(destination.parent))
            
            # Move file
            shutil.move(str(source), str(destination))
            
            self.logger.info(f"File moved successfully: {source_path} -> {destination_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to move file: {e}")
            return False
    
    def safe_delete_file(self, file_path: str) -> bool:
        """
        Safely delete file
        
        Args:
            file_path: Path to file to delete
            
        Returns:
            bool: True if deletion was successful
        """
        try:
            file_obj = Path(file_path)
            
            if not file_obj.exists():
                self.logger.warning(f"File does not exist: {file_path}")
                return True  # Consider it successful if file doesn't exist
            
            file_obj.unlink()
            self.logger.info(f"File deleted successfully: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete file {file_path}: {e}")
            return False
    
    def get_file_size(self, file_path: str) -> Optional[int]:
        """
        Get file size in bytes
        
        Args:
            file_path: Path to file
            
        Returns:
            Optional[int]: File size in bytes, None if error
        """
        try:
            return Path(file_path).stat().st_size
        except Exception as e:
            self.logger.error(f"Failed to get file size for {file_path}: {e}")
            return None
    
    def get_file_info(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        Get comprehensive file information
        
        Args:
            file_path: Path to file
            
        Returns:
            Optional[Dict[str, Any]]: File information dictionary
        """
        try:
            file_obj = Path(file_path)
            stat = file_obj.stat()
            
            return {
                'path': str(file_obj.absolute()),
                'name': file_obj.name,
                'size': stat.st_size,
                'size_mb': round(stat.st_size / (1024 * 1024), 2),
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'is_file': file_obj.is_file(),
                'is_directory': file_obj.is_dir(),
                'exists': file_obj.exists()
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get file info for {file_path}: {e}")
            return None
    
    def list_files(self, directory_path: str, pattern: str = "*", 
                  recursive: bool = False) -> List[str]:
        """
        List files in directory matching pattern
        
        Args:
            directory_path: Directory to search
            pattern: File pattern to match (e.g., "*.pdf")
            recursive: Whether to search recursively
            
        Returns:
            List[str]: List of matching file paths
        """
        try:
            directory = Path(directory_path)
            
            if not directory.exists() or not directory.is_dir():
                self.logger.error(f"Directory does not exist: {directory_path}")
                return []
            
            if recursive:
                files = list(directory.rglob(pattern))
            else:
                files = list(directory.glob(pattern))
            
            # Filter to only files (not directories)
            file_paths = [str(f) for f in files if f.is_file()]
            
            self.logger.debug(f"Found {len(file_paths)} files matching '{pattern}' in {directory_path}")
            return file_paths
            
        except Exception as e:
            self.logger.error(f"Failed to list files in {directory_path}: {e}")
            return []
    
    def clean_filename(self, filename: str) -> str:
        """
        Clean filename by removing invalid characters
        
        Args:
            filename: Original filename
            
        Returns:
            str: Cleaned filename
        """
        # Invalid characters for Windows filenames
        invalid_chars = '<>:"/\\|?*'
        
        cleaned = filename
        for char in invalid_chars:
            cleaned = cleaned.replace(char, '_')
        
        # Remove control characters
        cleaned = ''.join(char for char in cleaned if ord(char) >= 32)
        
        # Trim whitespace and dots from ends
        cleaned = cleaned.strip(' .')
        
        # Ensure filename is not empty
        if not cleaned:
            cleaned = "unnamed_file"
        
        return cleaned
    
    def generate_unique_filename(self, directory: str, base_name: str, 
                               extension: str = "") -> str:
        """
        Generate unique filename in directory
        
        Args:
            directory: Target directory
            base_name: Base filename
            extension: File extension (with or without dot)
            
        Returns:
            str: Unique filename
        """
        if extension and not extension.startswith('.'):
            extension = '.' + extension
        
        base_path = Path(directory) / f"{base_name}{extension}"
        
        if not base_path.exists():
            return str(base_path)
        
        # Generate numbered variants
        counter = 1
        while True:
            numbered_path = Path(directory) / f"{base_name}_{counter}{extension}"
            if not numbered_path.exists():
                return str(numbered_path)
            counter += 1
    
    def backup_file(self, file_path: str, backup_dir: Optional[str] = None) -> Optional[str]:
        """
        Create backup of file
        
        Args:
            file_path: Path to file to backup
            backup_dir: Directory for backup (default: same directory as original)
            
        Returns:
            Optional[str]: Path to backup file, None if failed
        """
        try:
            source = Path(file_path)
            
            if not source.exists():
                self.logger.error(f"Source file does not exist: {file_path}")
                return None
            
            # Determine backup directory
            if backup_dir:
                backup_directory = Path(backup_dir)
            else:
                backup_directory = source.parent
            
            # Ensure backup directory exists
            self.ensure_directory(str(backup_directory))
            
            # Generate backup filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{source.stem}_backup_{timestamp}{source.suffix}"
            backup_path = backup_directory / backup_name
            
            # Copy file to backup location
            shutil.copy2(source, backup_path)
            
            self.logger.info(f"File backed up: {file_path} -> {backup_path}")
            return str(backup_path)
            
        except Exception as e:
            self.logger.error(f"Failed to backup file {file_path}: {e}")
            return None
    
    def save_json(self, data: Any, file_path: str, indent: int = 2) -> bool:
        """
        Save data to JSON file
        
        Args:
            data: Data to save
            file_path: Path to JSON file
            indent: JSON indentation
            
        Returns:
            bool: True if successful
        """
        try:
            # Ensure directory exists
            self.ensure_directory(str(Path(file_path).parent))
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=indent)
            
            self.logger.debug(f"JSON data saved to: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save JSON to {file_path}: {e}")
            return False
    
    def load_json(self, file_path: str) -> Optional[Any]:
        """
        Load data from JSON file
        
        Args:
            file_path: Path to JSON file
            
        Returns:
            Optional[Any]: Loaded data, None if failed
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.logger.debug(f"JSON data loaded from: {file_path}")
            return data
            
        except Exception as e:
            self.logger.error(f"Failed to load JSON from {file_path}: {e}")
            return None


# Global file manager instance
_file_manager: Optional[FileManager] = None


def get_file_manager() -> FileManager:
    """
    Get global file manager instance
    
    Returns:
        FileManager: Global file manager instance
    """
    global _file_manager
    if _file_manager is None:
        _file_manager = FileManager()
    return _file_manager
