#!/usr/bin/env python3
"""
Project setup script for Chinese Pharmacopoeia PDF Splitter
Creates the necessary directory structure and initial files
"""

import os
from pathlib import Path

def create_project_structure():
    """Create the project directory structure"""
    
    # Define directory structure
    directories = [
        "src",
        "src/gui", 
        "src/core",
        "src/utils",
        "src/models",
        "tests",
        "tests/test_data",
        "logs",
        "output", 
        "temp"
    ]
    
    # Create directories
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {directory}")
    
    # Create __init__.py files for Python packages
    init_files = [
        "src/__init__.py",
        "src/gui/__init__.py",
        "src/core/__init__.py", 
        "src/utils/__init__.py",
        "src/models/__init__.py",
        "tests/__init__.py"
    ]
    
    for init_file in init_files:
        Path(init_file).touch()
        print(f"Created file: {init_file}")
    
    print("Project structure created successfully!")

if __name__ == "__main__":
    create_project_structure()
