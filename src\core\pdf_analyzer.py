"""
PDF Analyzer Module
Analyzes Chinese Pharmacopoeia PDF files to extract chapter information
"""

import re
import fitz  # PyMuPDF
import pdfplumber
from typing import List, Dict, Tuple, Optional, Any
from pathlib import Path
from datetime import datetime

try:
    from ..models.chapter_info import (
        ChapterInfo, PDFAnalysisResult, VolumeType, ChapterType, PageInfo
    )
    from ..utils.config_manager import get_config_manager
    from ..utils.logger import get_logger
except ImportError:
    # Fallback for direct execution
    from models.chapter_info import (
        ChapterInfo, PDFAnalysisResult, VolumeType, ChapterType, PageInfo
    )
    from utils.config_manager import get_config_manager
    from utils.logger import get_logger


class PDFAnalyzer:
    """
    PDF analyzer class for extracting chapter information from Chinese Pharmacopoeia PDFs
    Uses multiple PDF libraries for robust text extraction and analysis
    """
    
    def __init__(self):
        """Initialize PDF analyzer with configuration"""
        self.config_manager = get_config_manager()
        self.pdf_config = self.config_manager.get_pdf_config()
        self.logger = get_logger()
        
        # Chapter detection patterns
        self.chapter_patterns = self._build_chapter_patterns()
    
    def _build_chapter_patterns(self) -> List[re.Pattern]:
        """
        Build regex patterns for detecting chapter titles
        
        Returns:
            List[re.Pattern]: Compiled regex patterns for chapter detection
        """
        patterns = []
        
        # Pattern for numbered chapters (第X章, 第X部分, etc.)
        patterns.append(re.compile(r'第[一二三四五六七八九十\d]+[章部分篇].*', re.UNICODE))
        
        # Pattern for appendix (附录)
        patterns.append(re.compile(r'附录[A-Z\d]*.*', re.UNICODE))
        
        # Pattern for index (索引)
        patterns.append(re.compile(r'索引.*', re.UNICODE))
        
        # Pattern for general sections
        patterns.append(re.compile(r'[一二三四五六七八九十\d]+[\.、]\s*.*', re.UNICODE))
        
        # Custom patterns from config
        for keyword in self.pdf_config.chapter_keywords:
            pattern = re.compile(f'.*{re.escape(keyword)}.*', re.UNICODE)
            patterns.append(pattern)
        
        return patterns
    
    def analyze_pdf(self, pdf_path: str, volume: VolumeType) -> PDFAnalysisResult:
        """
        Analyze PDF file to extract chapter information
        
        Args:
            pdf_path: Path to PDF file
            volume: Volume type of the pharmacopoeia
            
        Returns:
            PDFAnalysisResult: Analysis result containing chapter information
        """
        self.logger.info(f"Starting PDF analysis: {pdf_path}")
        
        pdf_path_obj = Path(pdf_path)
        if not pdf_path_obj.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        # Initialize analysis result
        result = PDFAnalysisResult(
            pdf_path=str(pdf_path_obj.absolute()),
            volume=volume,
            total_pages=0,
            analysis_timestamp=datetime.now().isoformat()
        )
        
        try:
            # Use PyMuPDF for primary analysis
            result = self._analyze_with_pymupdf(pdf_path, result)
            
            # Validate and enhance with pdfplumber if needed
            result = self._enhance_with_pdfplumber(pdf_path, result)
            
            # Post-process chapters
            result = self._post_process_chapters(result)
            
            self.logger.info(f"PDF analysis completed. Found {len(result.chapters)} chapters")
            
        except Exception as e:
            error_msg = f"Error analyzing PDF: {str(e)}"
            self.logger.error(error_msg)
            result.analysis_errors.append(error_msg)
        
        return result
    
    def _analyze_with_pymupdf(self, pdf_path: str, result: PDFAnalysisResult) -> PDFAnalysisResult:
        """
        Analyze PDF using PyMuPDF library
        
        Args:
            pdf_path: Path to PDF file
            result: Analysis result to populate
            
        Returns:
            PDFAnalysisResult: Updated analysis result
        """
        try:
            doc = fitz.open(pdf_path)
            result.total_pages = len(doc)
            
            self.logger.info(f"Analyzing {result.total_pages} pages with PyMuPDF")
            
            page_infos = []
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                page_info = PageInfo(page_number=page_num + 1)
                
                # Extract text blocks with formatting information
                blocks = page.get_text("dict")
                
                for block in blocks.get("blocks", []):
                    if "lines" in block:
                        for line in block["lines"]:
                            for span in line.get("spans", []):
                                text = span.get("text", "").strip()
                                font_size = span.get("size", 0)
                                font_name = span.get("font", "")
                                
                                # Check if text matches chapter patterns
                                chapter_info = self._detect_chapter_title(
                                    text, font_size, font_name, page_num + 1
                                )
                                
                                if chapter_info:
                                    page_info.add_chapter_title(
                                        chapter_info.title, 
                                        chapter_info.confidence_score
                                    )
                                    result.add_chapter(chapter_info)
                
                page_infos.append(page_info)
                
                # Log progress every 50 pages
                if (page_num + 1) % 50 == 0:
                    self.logger.info(f"Processed {page_num + 1}/{result.total_pages} pages")
            
            doc.close()
            
        except Exception as e:
            error_msg = f"Error in PyMuPDF analysis: {str(e)}"
            self.logger.error(error_msg)
            result.analysis_errors.append(error_msg)
        
        return result
    
    def _enhance_with_pdfplumber(self, pdf_path: str, result: PDFAnalysisResult) -> PDFAnalysisResult:
        """
        Enhance analysis using pdfplumber for better text extraction
        
        Args:
            pdf_path: Path to PDF file
            result: Analysis result to enhance
            
        Returns:
            PDFAnalysisResult: Enhanced analysis result
        """
        try:
            with pdfplumber.open(pdf_path) as pdf:
                self.logger.info("Enhancing analysis with pdfplumber")
                
                for page_num, page in enumerate(pdf.pages):
                    # Extract text with better formatting preservation
                    text = page.extract_text()
                    if not text:
                        continue
                    
                    # Look for missed chapter titles
                    lines = text.split('\n')
                    for line in lines:
                        line = line.strip()
                        if line and self._is_potential_chapter_title(line):
                            # Check if we already detected this chapter
                            existing_chapter = self._find_existing_chapter(
                                result.chapters, line, page_num + 1
                            )
                            
                            if not existing_chapter:
                                chapter_info = ChapterInfo(
                                    title=line,
                                    start_page=page_num + 1,
                                    confidence_score=0.7  # Lower confidence for pdfplumber detection
                                )
                                result.add_chapter(chapter_info)
                
        except Exception as e:
            error_msg = f"Error in pdfplumber enhancement: {str(e)}"
            self.logger.error(error_msg)
            result.analysis_errors.append(error_msg)
        
        return result
    
    def _detect_chapter_title(self, text: str, font_size: float, font_name: str, 
                            page_num: int) -> Optional[ChapterInfo]:
        """
        Detect if text is a chapter title based on patterns and formatting
        
        Args:
            text: Text to analyze
            font_size: Font size of the text
            font_name: Font name of the text
            page_num: Page number where text was found
            
        Returns:
            Optional[ChapterInfo]: Chapter info if detected, None otherwise
        """
        if not text or len(text.strip()) < 2:
            return None
        
        text = text.strip()
        
        # Check against chapter patterns
        for pattern in self.chapter_patterns:
            if pattern.match(text):
                # Calculate confidence based on font size and pattern match
                confidence = self._calculate_confidence(text, font_size, font_name)
                
                if confidence >= self.pdf_config.confidence_threshold:
                    chapter_type = self._determine_chapter_type(text)
                    
                    return ChapterInfo(
                        title=text,
                        start_page=page_num,
                        chapter_type=chapter_type,
                        confidence_score=confidence,
                        font_size=font_size,
                        font_name=font_name
                    )
        
        return None
    
    def _calculate_confidence(self, text: str, font_size: float, font_name: str) -> float:
        """
        Calculate confidence score for chapter title detection
        
        Args:
            text: Text content
            font_size: Font size
            font_name: Font name
            
        Returns:
            float: Confidence score (0.0 to 1.0)
        """
        confidence = 0.5  # Base confidence
        
        # Boost confidence for specific patterns
        if re.match(r'第[一二三四五六七八九十\d]+章', text):
            confidence += 0.3
        elif re.match(r'附录[A-Z\d]*', text):
            confidence += 0.2
        elif any(keyword in text for keyword in self.pdf_config.chapter_keywords):
            confidence += 0.1
        
        # Boost confidence for larger font sizes
        if font_size > 12:
            confidence += min(0.2, (font_size - 12) * 0.02)
        
        # Boost confidence for bold fonts
        if font_name and ('bold' in font_name.lower() or 'black' in font_name.lower()):
            confidence += 0.1
        
        return min(1.0, confidence)
    
    def _determine_chapter_type(self, text: str) -> ChapterType:
        """
        Determine chapter type based on text content
        
        Args:
            text: Chapter title text
            
        Returns:
            ChapterType: Determined chapter type
        """
        text_lower = text.lower()
        
        if '附录' in text or 'appendix' in text_lower:
            return ChapterType.APPENDIX
        elif '索引' in text or 'index' in text_lower:
            return ChapterType.INDEX
        elif re.match(r'第[一二三四五六七八九十\d]+章', text):
            return ChapterType.MAIN_CHAPTER
        else:
            return ChapterType.SUB_CHAPTER
    
    def _is_potential_chapter_title(self, text: str) -> bool:
        """
        Check if text could be a chapter title (for pdfplumber enhancement)
        
        Args:
            text: Text to check
            
        Returns:
            bool: True if potentially a chapter title
        """
        if not text or len(text.strip()) < 2:
            return False
        
        text = text.strip()
        
        # Check length (chapter titles are usually not too long)
        if len(text) > 100:
            return False
        
        # Check for chapter patterns
        for pattern in self.chapter_patterns:
            if pattern.match(text):
                return True
        
        return False
    
    def _find_existing_chapter(self, chapters: List[ChapterInfo], title: str, 
                             page_num: int) -> Optional[ChapterInfo]:
        """
        Find existing chapter with similar title and page number
        
        Args:
            chapters: List of existing chapters
            title: Title to search for
            page_num: Page number to match
            
        Returns:
            Optional[ChapterInfo]: Existing chapter if found
        """
        for chapter in chapters:
            if (chapter.start_page == page_num and 
                self._titles_similar(chapter.title, title)):
                return chapter
        return None
    
    def _titles_similar(self, title1: str, title2: str, threshold: float = 0.8) -> bool:
        """
        Check if two titles are similar
        
        Args:
            title1: First title
            title2: Second title
            threshold: Similarity threshold
            
        Returns:
            bool: True if titles are similar
        """
        # Simple similarity check based on common characters
        title1_clean = re.sub(r'\s+', '', title1.lower())
        title2_clean = re.sub(r'\s+', '', title2.lower())
        
        if not title1_clean or not title2_clean:
            return False
        
        # Calculate Jaccard similarity
        set1 = set(title1_clean)
        set2 = set(title2_clean)
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        similarity = intersection / union if union > 0 else 0
        return similarity >= threshold
    
    def _post_process_chapters(self, result: PDFAnalysisResult) -> PDFAnalysisResult:
        """
        Post-process detected chapters to set end pages and clean up
        
        Args:
            result: Analysis result to post-process
            
        Returns:
            PDFAnalysisResult: Post-processed result
        """
        if not result.chapters:
            return result
        
        # Sort chapters by start page
        result.chapters.sort(key=lambda ch: ch.start_page)
        
        # Set end pages for each chapter
        for i, chapter in enumerate(result.chapters):
            if i < len(result.chapters) - 1:
                # End page is one before the next chapter starts
                next_chapter = result.chapters[i + 1]
                chapter.set_end_page(next_chapter.start_page - 1)
            else:
                # Last chapter goes to the end of the document
                chapter.set_end_page(result.total_pages)
        
        # Remove chapters with invalid page ranges
        valid_chapters = []
        for chapter in result.chapters:
            if chapter.page_count > 0:
                valid_chapters.append(chapter)
            else:
                self.logger.warning(f"Removing invalid chapter: {chapter.title}")
        
        result.chapters = valid_chapters
        
        self.logger.info(f"Post-processing completed. {len(result.chapters)} valid chapters")
        
        return result

    def save_analysis_result(self, result: PDFAnalysisResult, output_path: str) -> None:
        """
        Save analysis result to JSON file

        Args:
            result: Analysis result to save
            output_path: Path to save the result
        """
        try:
            result.save_to_json(output_path)
            self.logger.info(f"Analysis result saved to: {output_path}")
        except Exception as e:
            self.logger.error(f"Failed to save analysis result: {e}")
            raise

    def load_analysis_result(self, json_path: str) -> PDFAnalysisResult:
        """
        Load analysis result from JSON file

        Args:
            json_path: Path to JSON file

        Returns:
            PDFAnalysisResult: Loaded analysis result
        """
        try:
            result = PDFAnalysisResult.load_from_json(json_path)
            self.logger.info(f"Analysis result loaded from: {json_path}")
            return result
        except Exception as e:
            self.logger.error(f"Failed to load analysis result: {e}")
            raise
